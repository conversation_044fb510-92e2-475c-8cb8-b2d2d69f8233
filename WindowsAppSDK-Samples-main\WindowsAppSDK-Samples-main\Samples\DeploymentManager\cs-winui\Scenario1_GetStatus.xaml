﻿<!-- Copyright (c) Microsoft Corporation.
     Licensed under the MIT License. -->
<Page
    x:Class="DeploymentManagerSample.Scenario1_GetStatus"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:DeploymentManagerSample"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d">

    <Grid RowDefinitions="*,Auto">
        <ScrollViewer Background="{ThemeResource ApplicationPageBackgroundThemeBrush}">
            <StackPanel Spacing="10" Margin="10,10,10,10">
                <TextBlock Text="Description:" Style="{StaticResource SampleHeaderTextStyle}"/>
                <TextBlock Style="{StaticResource ScenarioDescriptionTextStyle}" TextWrapping="Wrap">
                    Get current status of Windows App SDK runtime using DeploymentManager.GetStatus();
                </TextBlock>
                <Button Content="Get deployment status of Windows App SDK Runtime" Click="GetStatus_Click" />
                <TextBlock x:Name="resultStatus"></TextBlock>
                <TextBlock x:Name="resultExtendedError" TextWrapping="Wrap"></TextBlock>
                <TextBlock x:Name="resultImplication"></TextBlock>
            </StackPanel>
        </ScrollViewer>
    </Grid>
</Page>
