﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Machine>DESKTOP-N6K8R2J</Machine>
    <WindowsUser>ivane</WindowsUser>
    <TargetPlatformIdentifier>Windows</TargetPlatformIdentifier>
    <TargetOsVersion>10.0</TargetOsVersion>
    <TargetOsDescription>Windows 10.0</TargetOsDescription>
    <SolutionConfiguration>Debug|x64</SolutionConfiguration>
    <PackageArchitecture>x64</PackageArchitecture>
    <PackageIdentityName>d8982488-654b-4863-b09e-47f22e166952</PackageIdentityName>
    <PackageIdentityPublisher>CN=ivane</PackageIdentityPublisher>
    <IntermediateOutputPath>D:\Software3\PrestoIves\PrestoIves\PrestoIves\obj\x64\Debug\net8.0-windows10.0.19041.0\win-x64\</IntermediateOutputPath>
    <RemoteDeploymentType>CopyToDevice</RemoteDeploymentType>
    <PackageRegistrationPath>
    </PackageRegistrationPath>
    <RemoveNonLayoutFiles>false</RemoveNonLayoutFiles>
    <DeployOptionalPackages>false</DeployOptionalPackages>
    <WindowsSdkPath>C:\Program Files %28x86%29\Windows Kits\10\</WindowsSdkPath>
    <LayoutDir>D:\Software3\PrestoIves\PrestoIves\PrestoIves\bin\x64\Debug\net8.0-windows10.0.19041.0\win-x64\AppX</LayoutDir>
    <RegisteredManifestChecksum>00535AFE4C5032D784595F571E90F0E4A6CDB07302A758F8EFB0BADDA7A2590A</RegisteredManifestChecksum>
    <RegisteredPackageMoniker>d8982488-654b-4863-b09e-47f22e166952_1.0.0.0_x64__7z6da736jhk6w</RegisteredPackageMoniker>
    <RegisteredUserModeAppID>d8982488-654b-4863-b09e-47f22e166952_7z6da736jhk6w!App</RegisteredUserModeAppID>
    <RegisteredPackageID>d8982488-654b-4863-b09e-47f22e166952</RegisteredPackageID>
    <RegisteredPackagePublisher>CN=ivane</RegisteredPackagePublisher>
    <RegisteredVersion>1.0.0.0</RegisteredVersion>
  </PropertyGroup>
  <ItemGroup>
    <AppXManifest Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\bin\x64\Debug\net8.0-windows10.0.19041.0\win-x64\AppxManifest.xml">
      <PackagePath>AppxManifest.xml</PackagePath>
      <ReRegisterAppIfChanged>true</ReRegisterAppIfChanged>
      <Modified>2025-09-14T15:15:16.612</Modified>
    </AppXManifest>
  </ItemGroup>
  <ItemGroup>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\bin\x64\Debug\net8.0-windows10.0.19041.0\win-x64\PrestoIves.runtimeconfig.json">
      <PackagePath>PrestoIves.runtimeconfig.json</PackagePath>
      <Modified>2025-09-12T23:05:41.351</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\obj\x64\Debug\net8.0-windows10.0.19041.0\win-x64\apphost.exe">
      <PackagePath>PrestoIves.exe</PackagePath>
      <Modified>2025-09-14T15:15:15.737</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\obj\x64\Debug\net8.0-windows10.0.19041.0\win-x64\PrestoIves.dll">
      <PackagePath>PrestoIves.dll</PackagePath>
      <Modified>2025-09-14T15:15:15.659</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\Microsoft.CSharp.dll">
      <PackagePath>Microsoft.CSharp.dll</PackagePath>
      <Modified>2025-07-15T18:06:52.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\Microsoft.VisualBasic.Core.dll">
      <PackagePath>Microsoft.VisualBasic.Core.dll</PackagePath>
      <Modified>2025-07-15T18:06:56.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\Microsoft.VisualBasic.dll">
      <PackagePath>Microsoft.VisualBasic.dll</PackagePath>
      <Modified>2025-07-15T18:01:06.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\Microsoft.Win32.Primitives.dll">
      <PackagePath>Microsoft.Win32.Primitives.dll</PackagePath>
      <Modified>2025-07-15T18:00:08.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\Microsoft.Win32.Registry.dll">
      <PackagePath>Microsoft.Win32.Registry.dll</PackagePath>
      <Modified>2025-07-15T18:06:58.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.AppContext.dll">
      <PackagePath>System.AppContext.dll</PackagePath>
      <Modified>2025-07-15T18:01:02.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Buffers.dll">
      <PackagePath>System.Buffers.dll</PackagePath>
      <Modified>2025-07-15T18:00:58.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Collections.Concurrent.dll">
      <PackagePath>System.Collections.Concurrent.dll</PackagePath>
      <Modified>2025-07-15T18:07:02.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Collections.Immutable.dll">
      <PackagePath>System.Collections.Immutable.dll</PackagePath>
      <Modified>2025-07-15T18:07:08.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Collections.NonGeneric.dll">
      <PackagePath>System.Collections.NonGeneric.dll</PackagePath>
      <Modified>2025-07-15T18:07:10.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Collections.Specialized.dll">
      <PackagePath>System.Collections.Specialized.dll</PackagePath>
      <Modified>2025-07-15T18:07:14.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Collections.dll">
      <PackagePath>System.Collections.dll</PackagePath>
      <Modified>2025-07-15T18:07:04.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.ComponentModel.Annotations.dll">
      <PackagePath>System.ComponentModel.Annotations.dll</PackagePath>
      <Modified>2025-07-15T18:07:16.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.ComponentModel.DataAnnotations.dll">
      <PackagePath>System.ComponentModel.DataAnnotations.dll</PackagePath>
      <Modified>2025-07-15T18:00:58.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.ComponentModel.EventBasedAsync.dll">
      <PackagePath>System.ComponentModel.EventBasedAsync.dll</PackagePath>
      <Modified>2025-07-15T18:07:22.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.ComponentModel.Primitives.dll">
      <PackagePath>System.ComponentModel.Primitives.dll</PackagePath>
      <Modified>2025-07-15T18:07:26.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.ComponentModel.TypeConverter.dll">
      <PackagePath>System.ComponentModel.TypeConverter.dll</PackagePath>
      <Modified>2025-07-15T18:07:28.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.ComponentModel.dll">
      <PackagePath>System.ComponentModel.dll</PackagePath>
      <Modified>2025-07-15T18:07:20.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Configuration.dll">
      <PackagePath>System.Configuration.dll</PackagePath>
      <Modified>2025-07-15T18:01:08.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Console.dll">
      <PackagePath>System.Console.dll</PackagePath>
      <Modified>2025-07-15T18:07:32.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Core.dll">
      <PackagePath>System.Core.dll</PackagePath>
      <Modified>2025-07-15T18:01:48.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Data.Common.dll">
      <PackagePath>System.Data.Common.dll</PackagePath>
      <Modified>2025-07-15T18:07:36.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Data.DataSetExtensions.dll">
      <PackagePath>System.Data.DataSetExtensions.dll</PackagePath>
      <Modified>2025-07-15T18:01:30.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Data.dll">
      <PackagePath>System.Data.dll</PackagePath>
      <Modified>2025-07-15T18:01:30.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Diagnostics.Contracts.dll">
      <PackagePath>System.Diagnostics.Contracts.dll</PackagePath>
      <Modified>2025-07-15T18:00:14.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Diagnostics.Debug.dll">
      <PackagePath>System.Diagnostics.Debug.dll</PackagePath>
      <Modified>2025-07-15T18:00:56.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Diagnostics.DiagnosticSource.dll">
      <PackagePath>System.Diagnostics.DiagnosticSource.dll</PackagePath>
      <Modified>2025-07-15T18:07:38.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Diagnostics.FileVersionInfo.dll">
      <PackagePath>System.Diagnostics.FileVersionInfo.dll</PackagePath>
      <Modified>2025-07-15T18:07:42.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Diagnostics.Process.dll">
      <PackagePath>System.Diagnostics.Process.dll</PackagePath>
      <Modified>2025-07-15T18:07:44.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Diagnostics.StackTrace.dll">
      <PackagePath>System.Diagnostics.StackTrace.dll</PackagePath>
      <Modified>2025-07-15T18:07:48.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Diagnostics.TextWriterTraceListener.dll">
      <PackagePath>System.Diagnostics.TextWriterTraceListener.dll</PackagePath>
      <Modified>2025-07-15T18:07:50.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Diagnostics.Tools.dll">
      <PackagePath>System.Diagnostics.Tools.dll</PackagePath>
      <Modified>2025-07-15T18:01:02.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Diagnostics.TraceSource.dll">
      <PackagePath>System.Diagnostics.TraceSource.dll</PackagePath>
      <Modified>2025-07-15T18:07:54.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Diagnostics.Tracing.dll">
      <PackagePath>System.Diagnostics.Tracing.dll</PackagePath>
      <Modified>2025-07-15T17:59:46.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Drawing.Primitives.dll">
      <PackagePath>System.Drawing.Primitives.dll</PackagePath>
      <Modified>2025-07-15T18:07:56.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Drawing.dll">
      <PackagePath>System.Drawing.dll</PackagePath>
      <Modified>2025-07-15T18:01:10.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Dynamic.Runtime.dll">
      <PackagePath>System.Dynamic.Runtime.dll</PackagePath>
      <Modified>2025-07-15T18:00:56.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Formats.Asn1.dll">
      <PackagePath>System.Formats.Asn1.dll</PackagePath>
      <Modified>2025-07-15T18:07:58.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Formats.Tar.dll">
      <PackagePath>System.Formats.Tar.dll</PackagePath>
      <Modified>2025-07-15T18:08:02.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Globalization.Calendars.dll">
      <PackagePath>System.Globalization.Calendars.dll</PackagePath>
      <Modified>2025-07-15T18:01:02.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Globalization.Extensions.dll">
      <PackagePath>System.Globalization.Extensions.dll</PackagePath>
      <Modified>2025-07-15T18:00:56.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Globalization.dll">
      <PackagePath>System.Globalization.dll</PackagePath>
      <Modified>2025-07-15T18:01:02.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.IO.Compression.Brotli.dll">
      <PackagePath>System.IO.Compression.Brotli.dll</PackagePath>
      <Modified>2025-07-15T18:08:04.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.IO.Compression.FileSystem.dll">
      <PackagePath>System.IO.Compression.FileSystem.dll</PackagePath>
      <Modified>2025-07-15T18:01:06.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.IO.Compression.ZipFile.dll">
      <PackagePath>System.IO.Compression.ZipFile.dll</PackagePath>
      <Modified>2025-07-15T18:08:10.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.IO.Compression.dll">
      <PackagePath>System.IO.Compression.dll</PackagePath>
      <Modified>2025-07-15T18:08:08.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.IO.FileSystem.AccessControl.dll">
      <PackagePath>System.IO.FileSystem.AccessControl.dll</PackagePath>
      <Modified>2025-07-15T18:08:14.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.IO.FileSystem.DriveInfo.dll">
      <PackagePath>System.IO.FileSystem.DriveInfo.dll</PackagePath>
      <Modified>2025-07-15T18:08:16.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.IO.FileSystem.Primitives.dll">
      <PackagePath>System.IO.FileSystem.Primitives.dll</PackagePath>
      <Modified>2025-07-15T18:00:58.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.IO.FileSystem.Watcher.dll">
      <PackagePath>System.IO.FileSystem.Watcher.dll</PackagePath>
      <Modified>2025-07-15T18:08:20.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.IO.FileSystem.dll">
      <PackagePath>System.IO.FileSystem.dll</PackagePath>
      <Modified>2025-07-15T18:00:52.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.IO.IsolatedStorage.dll">
      <PackagePath>System.IO.IsolatedStorage.dll</PackagePath>
      <Modified>2025-07-15T18:08:22.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.IO.MemoryMappedFiles.dll">
      <PackagePath>System.IO.MemoryMappedFiles.dll</PackagePath>
      <Modified>2025-07-15T18:08:26.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.IO.Pipes.AccessControl.dll">
      <PackagePath>System.IO.Pipes.AccessControl.dll</PackagePath>
      <Modified>2025-07-15T17:58:22.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.IO.Pipes.dll">
      <PackagePath>System.IO.Pipes.dll</PackagePath>
      <Modified>2025-07-15T18:08:28.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.IO.UnmanagedMemoryStream.dll">
      <PackagePath>System.IO.UnmanagedMemoryStream.dll</PackagePath>
      <Modified>2025-07-15T18:01:48.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.IO.dll">
      <PackagePath>System.IO.dll</PackagePath>
      <Modified>2025-07-15T18:00:52.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Linq.Expressions.dll">
      <PackagePath>System.Linq.Expressions.dll</PackagePath>
      <Modified>2025-07-15T18:08:36.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Linq.Parallel.dll">
      <PackagePath>System.Linq.Parallel.dll</PackagePath>
      <Modified>2025-07-15T18:08:40.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Linq.Queryable.dll">
      <PackagePath>System.Linq.Queryable.dll</PackagePath>
      <Modified>2025-07-15T18:08:44.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Linq.dll">
      <PackagePath>System.Linq.dll</PackagePath>
      <Modified>2025-07-15T18:08:32.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Memory.dll">
      <PackagePath>System.Memory.dll</PackagePath>
      <Modified>2025-07-15T18:08:46.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Net.Http.Json.dll">
      <PackagePath>System.Net.Http.Json.dll</PackagePath>
      <Modified>2025-07-15T18:08:54.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Net.Http.dll">
      <PackagePath>System.Net.Http.dll</PackagePath>
      <Modified>2025-07-15T18:08:50.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Net.HttpListener.dll">
      <PackagePath>System.Net.HttpListener.dll</PackagePath>
      <Modified>2025-07-15T18:08:56.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Net.Mail.dll">
      <PackagePath>System.Net.Mail.dll</PackagePath>
      <Modified>2025-07-15T18:09:00.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Net.NameResolution.dll">
      <PackagePath>System.Net.NameResolution.dll</PackagePath>
      <Modified>2025-07-15T18:09:02.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Net.NetworkInformation.dll">
      <PackagePath>System.Net.NetworkInformation.dll</PackagePath>
      <Modified>2025-07-15T18:09:06.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Net.Ping.dll">
      <PackagePath>System.Net.Ping.dll</PackagePath>
      <Modified>2025-07-15T18:09:08.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Net.Primitives.dll">
      <PackagePath>System.Net.Primitives.dll</PackagePath>
      <Modified>2025-07-15T18:09:12.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Net.Quic.dll">
      <PackagePath>System.Net.Quic.dll</PackagePath>
      <Modified>2025-07-15T18:09:14.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Net.Requests.dll">
      <PackagePath>System.Net.Requests.dll</PackagePath>
      <Modified>2025-07-15T18:09:18.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Net.Security.dll">
      <PackagePath>System.Net.Security.dll</PackagePath>
      <Modified>2025-07-15T18:09:20.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Net.ServicePoint.dll">
      <PackagePath>System.Net.ServicePoint.dll</PackagePath>
      <Modified>2025-07-15T18:09:22.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Net.Sockets.dll">
      <PackagePath>System.Net.Sockets.dll</PackagePath>
      <Modified>2025-07-15T18:09:26.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Net.WebClient.dll">
      <PackagePath>System.Net.WebClient.dll</PackagePath>
      <Modified>2025-07-15T18:09:28.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Net.WebHeaderCollection.dll">
      <PackagePath>System.Net.WebHeaderCollection.dll</PackagePath>
      <Modified>2025-07-15T18:09:32.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Net.WebProxy.dll">
      <PackagePath>System.Net.WebProxy.dll</PackagePath>
      <Modified>2025-07-15T18:09:34.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Net.WebSockets.Client.dll">
      <PackagePath>System.Net.WebSockets.Client.dll</PackagePath>
      <Modified>2025-07-15T18:09:38.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Net.WebSockets.dll">
      <PackagePath>System.Net.WebSockets.dll</PackagePath>
      <Modified>2025-07-15T18:09:40.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Net.dll">
      <PackagePath>System.Net.dll</PackagePath>
      <Modified>2025-07-15T18:01:18.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Numerics.Vectors.dll">
      <PackagePath>System.Numerics.Vectors.dll</PackagePath>
      <Modified>2025-07-15T18:01:00.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Numerics.dll">
      <PackagePath>System.Numerics.dll</PackagePath>
      <Modified>2025-07-15T18:01:10.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.ObjectModel.dll">
      <PackagePath>System.ObjectModel.dll</PackagePath>
      <Modified>2025-07-15T18:09:44.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Private.CoreLib.dll">
      <PackagePath>System.Private.CoreLib.dll</PackagePath>
      <Modified>2025-07-15T17:54:14.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Private.DataContractSerialization.dll">
      <PackagePath>System.Private.DataContractSerialization.dll</PackagePath>
      <Modified>2025-07-15T18:09:46.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Private.Uri.dll">
      <PackagePath>System.Private.Uri.dll</PackagePath>
      <Modified>2025-07-15T18:09:50.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Private.Xml.Linq.dll">
      <PackagePath>System.Private.Xml.Linq.dll</PackagePath>
      <Modified>2025-07-15T18:09:58.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Private.Xml.dll">
      <PackagePath>System.Private.Xml.dll</PackagePath>
      <Modified>2025-07-15T18:09:56.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Reflection.DispatchProxy.dll">
      <PackagePath>System.Reflection.DispatchProxy.dll</PackagePath>
      <Modified>2025-07-15T18:10:02.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Reflection.Emit.ILGeneration.dll">
      <PackagePath>System.Reflection.Emit.ILGeneration.dll</PackagePath>
      <Modified>2025-07-15T18:00:16.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Reflection.Emit.Lightweight.dll">
      <PackagePath>System.Reflection.Emit.Lightweight.dll</PackagePath>
      <Modified>2025-07-15T18:00:06.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Reflection.Emit.dll">
      <PackagePath>System.Reflection.Emit.dll</PackagePath>
      <Modified>2025-07-15T18:10:04.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Reflection.Extensions.dll">
      <PackagePath>System.Reflection.Extensions.dll</PackagePath>
      <Modified>2025-07-15T18:00:56.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Reflection.Metadata.dll">
      <PackagePath>System.Reflection.Metadata.dll</PackagePath>
      <Modified>2025-07-15T18:10:08.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Reflection.Primitives.dll">
      <PackagePath>System.Reflection.Primitives.dll</PackagePath>
      <Modified>2025-07-15T18:00:40.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Reflection.TypeExtensions.dll">
      <PackagePath>System.Reflection.TypeExtensions.dll</PackagePath>
      <Modified>2025-07-15T18:10:10.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Reflection.dll">
      <PackagePath>System.Reflection.dll</PackagePath>
      <Modified>2025-07-15T18:00:52.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Resources.Reader.dll">
      <PackagePath>System.Resources.Reader.dll</PackagePath>
      <Modified>2025-07-15T18:01:04.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Resources.ResourceManager.dll">
      <PackagePath>System.Resources.ResourceManager.dll</PackagePath>
      <Modified>2025-07-15T18:01:04.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Resources.Writer.dll">
      <PackagePath>System.Resources.Writer.dll</PackagePath>
      <Modified>2025-07-15T18:10:14.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Runtime.CompilerServices.Unsafe.dll">
      <PackagePath>System.Runtime.CompilerServices.Unsafe.dll</PackagePath>
      <Modified>2025-07-15T18:00:54.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Runtime.CompilerServices.VisualC.dll">
      <PackagePath>System.Runtime.CompilerServices.VisualC.dll</PackagePath>
      <Modified>2025-07-15T18:10:16.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Runtime.Extensions.dll">
      <PackagePath>System.Runtime.Extensions.dll</PackagePath>
      <Modified>2025-07-15T18:00:52.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Runtime.Handles.dll">
      <PackagePath>System.Runtime.Handles.dll</PackagePath>
      <Modified>2025-07-15T18:00:56.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Runtime.InteropServices.JavaScript.dll">
      <PackagePath>System.Runtime.InteropServices.JavaScript.dll</PackagePath>
      <Modified>2025-07-15T18:10:22.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Runtime.InteropServices.RuntimeInformation.dll">
      <PackagePath>System.Runtime.InteropServices.RuntimeInformation.dll</PackagePath>
      <Modified>2025-07-15T18:01:04.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Runtime.InteropServices.dll">
      <PackagePath>System.Runtime.InteropServices.dll</PackagePath>
      <Modified>2025-07-15T18:10:20.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Runtime.Intrinsics.dll">
      <PackagePath>System.Runtime.Intrinsics.dll</PackagePath>
      <Modified>2025-07-15T18:00:22.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Runtime.Loader.dll">
      <PackagePath>System.Runtime.Loader.dll</PackagePath>
      <Modified>2025-07-15T18:00:22.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Runtime.Numerics.dll">
      <PackagePath>System.Runtime.Numerics.dll</PackagePath>
      <Modified>2025-07-15T18:10:24.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Runtime.Serialization.Formatters.dll">
      <PackagePath>System.Runtime.Serialization.Formatters.dll</PackagePath>
      <Modified>2025-07-15T18:10:28.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Runtime.Serialization.Json.dll">
      <PackagePath>System.Runtime.Serialization.Json.dll</PackagePath>
      <Modified>2025-07-15T18:00:12.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Runtime.Serialization.Primitives.dll">
      <PackagePath>System.Runtime.Serialization.Primitives.dll</PackagePath>
      <Modified>2025-07-15T18:10:30.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Runtime.Serialization.Xml.dll">
      <PackagePath>System.Runtime.Serialization.Xml.dll</PackagePath>
      <Modified>2025-07-15T18:00:10.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Runtime.Serialization.dll">
      <PackagePath>System.Runtime.Serialization.dll</PackagePath>
      <Modified>2025-07-15T18:01:10.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Runtime.dll">
      <PackagePath>System.Runtime.dll</PackagePath>
      <Modified>2025-07-15T18:00:40.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Security.AccessControl.dll">
      <PackagePath>System.Security.AccessControl.dll</PackagePath>
      <Modified>2025-07-15T18:10:34.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Security.Claims.dll">
      <PackagePath>System.Security.Claims.dll</PackagePath>
      <Modified>2025-07-15T18:10:36.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Security.Cryptography.Algorithms.dll">
      <PackagePath>System.Security.Cryptography.Algorithms.dll</PackagePath>
      <Modified>2025-07-15T18:01:06.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Security.Cryptography.Cng.dll">
      <PackagePath>System.Security.Cryptography.Cng.dll</PackagePath>
      <Modified>2025-07-15T18:01:06.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Security.Cryptography.Csp.dll">
      <PackagePath>System.Security.Cryptography.Csp.dll</PackagePath>
      <Modified>2025-07-15T18:01:06.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Security.Cryptography.Encoding.dll">
      <PackagePath>System.Security.Cryptography.Encoding.dll</PackagePath>
      <Modified>2025-07-15T18:01:06.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Security.Cryptography.OpenSsl.dll">
      <PackagePath>System.Security.Cryptography.OpenSsl.dll</PackagePath>
      <Modified>2025-07-15T18:01:08.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Security.Cryptography.Primitives.dll">
      <PackagePath>System.Security.Cryptography.Primitives.dll</PackagePath>
      <Modified>2025-07-15T18:01:08.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Security.Cryptography.X509Certificates.dll">
      <PackagePath>System.Security.Cryptography.X509Certificates.dll</PackagePath>
      <Modified>2025-07-15T18:01:08.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Security.Cryptography.dll">
      <PackagePath>System.Security.Cryptography.dll</PackagePath>
      <Modified>2025-07-15T18:10:40.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Security.Principal.Windows.dll">
      <PackagePath>System.Security.Principal.Windows.dll</PackagePath>
      <Modified>2025-07-15T18:10:44.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Security.Principal.dll">
      <PackagePath>System.Security.Principal.dll</PackagePath>
      <Modified>2025-07-15T18:00:56.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Security.SecureString.dll">
      <PackagePath>System.Security.SecureString.dll</PackagePath>
      <Modified>2025-07-15T18:01:48.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Security.dll">
      <PackagePath>System.Security.dll</PackagePath>
      <Modified>2025-07-15T18:01:08.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.ServiceModel.Web.dll">
      <PackagePath>System.ServiceModel.Web.dll</PackagePath>
      <Modified>2025-07-15T18:01:10.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.ServiceProcess.dll">
      <PackagePath>System.ServiceProcess.dll</PackagePath>
      <Modified>2025-07-15T18:01:10.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Text.Encoding.CodePages.dll">
      <PackagePath>System.Text.Encoding.CodePages.dll</PackagePath>
      <Modified>2025-07-15T18:10:46.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Text.Encoding.Extensions.dll">
      <PackagePath>System.Text.Encoding.Extensions.dll</PackagePath>
      <Modified>2025-07-15T18:01:14.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Text.Encoding.dll">
      <PackagePath>System.Text.Encoding.dll</PackagePath>
      <Modified>2025-07-15T18:00:52.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Text.Encodings.Web.dll">
      <PackagePath>System.Text.Encodings.Web.dll</PackagePath>
      <Modified>2025-07-15T18:10:50.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Text.Json.dll">
      <PackagePath>System.Text.Json.dll</PackagePath>
      <Modified>2025-07-15T18:10:52.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Text.RegularExpressions.dll">
      <PackagePath>System.Text.RegularExpressions.dll</PackagePath>
      <Modified>2025-07-15T18:10:56.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Threading.Channels.dll">
      <PackagePath>System.Threading.Channels.dll</PackagePath>
      <Modified>2025-07-15T18:11:00.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Threading.Overlapped.dll">
      <PackagePath>System.Threading.Overlapped.dll</PackagePath>
      <Modified>2025-07-15T18:00:22.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Threading.Tasks.Dataflow.dll">
      <PackagePath>System.Threading.Tasks.Dataflow.dll</PackagePath>
      <Modified>2025-07-15T18:11:06.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Threading.Tasks.Extensions.dll">
      <PackagePath>System.Threading.Tasks.Extensions.dll</PackagePath>
      <Modified>2025-07-15T18:00:54.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Threading.Tasks.Parallel.dll">
      <PackagePath>System.Threading.Tasks.Parallel.dll</PackagePath>
      <Modified>2025-07-15T18:11:08.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Threading.Tasks.dll">
      <PackagePath>System.Threading.Tasks.dll</PackagePath>
      <Modified>2025-07-15T18:00:54.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Threading.Thread.dll">
      <PackagePath>System.Threading.Thread.dll</PackagePath>
      <Modified>2025-07-15T17:59:48.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Threading.ThreadPool.dll">
      <PackagePath>System.Threading.ThreadPool.dll</PackagePath>
      <Modified>2025-07-15T17:59:50.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Threading.Timer.dll">
      <PackagePath>System.Threading.Timer.dll</PackagePath>
      <Modified>2025-07-15T18:01:04.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Threading.dll">
      <PackagePath>System.Threading.dll</PackagePath>
      <Modified>2025-07-15T18:11:02.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Transactions.Local.dll">
      <PackagePath>System.Transactions.Local.dll</PackagePath>
      <Modified>2025-07-15T18:11:12.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Transactions.dll">
      <PackagePath>System.Transactions.dll</PackagePath>
      <Modified>2025-07-15T18:01:08.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.ValueTuple.dll">
      <PackagePath>System.ValueTuple.dll</PackagePath>
      <Modified>2025-07-15T18:00:52.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Web.HttpUtility.dll">
      <PackagePath>System.Web.HttpUtility.dll</PackagePath>
      <Modified>2025-07-15T18:11:14.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Web.dll">
      <PackagePath>System.Web.dll</PackagePath>
      <Modified>2025-07-15T18:00:52.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Windows.dll">
      <PackagePath>System.Windows.dll</PackagePath>
      <Modified>2025-07-15T18:01:00.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Xml.Linq.dll">
      <PackagePath>System.Xml.Linq.dll</PackagePath>
      <Modified>2025-07-15T18:01:18.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Xml.ReaderWriter.dll">
      <PackagePath>System.Xml.ReaderWriter.dll</PackagePath>
      <Modified>2025-07-15T18:00:58.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Xml.Serialization.dll">
      <PackagePath>System.Xml.Serialization.dll</PackagePath>
      <Modified>2025-07-15T18:01:02.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Xml.XDocument.dll">
      <PackagePath>System.Xml.XDocument.dll</PackagePath>
      <Modified>2025-07-15T18:01:18.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Xml.XPath.XDocument.dll">
      <PackagePath>System.Xml.XPath.XDocument.dll</PackagePath>
      <Modified>2025-07-15T18:11:18.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Xml.XPath.dll">
      <PackagePath>System.Xml.XPath.dll</PackagePath>
      <Modified>2025-07-15T18:01:16.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Xml.XmlDocument.dll">
      <PackagePath>System.Xml.XmlDocument.dll</PackagePath>
      <Modified>2025-07-15T18:01:06.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Xml.XmlSerializer.dll">
      <PackagePath>System.Xml.XmlSerializer.dll</PackagePath>
      <Modified>2025-07-15T18:00:58.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Xml.dll">
      <PackagePath>System.Xml.dll</PackagePath>
      <Modified>2025-07-15T18:01:32.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.dll">
      <PackagePath>System.dll</PackagePath>
      <Modified>2025-07-15T18:01:52.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\WindowsBase.dll">
      <PackagePath>WindowsBase.dll</PackagePath>
      <Modified>2025-07-15T18:01:08.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\mscorlib.dll">
      <PackagePath>mscorlib.dll</PackagePath>
      <Modified>2025-07-15T18:01:56.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\netstandard.dll">
      <PackagePath>netstandard.dll</PackagePath>
      <Modified>2025-07-15T18:01:54.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\native\Microsoft.DiaSymReader.Native.amd64.dll">
      <PackagePath>Microsoft.DiaSymReader.Native.amd64.dll</PackagePath>
      <Modified>2024-11-27T04:18:14.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\native\System.IO.Compression.Native.dll">
      <PackagePath>System.IO.Compression.Native.dll</PackagePath>
      <Modified>2025-07-15T17:57:24.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\native\clretwrc.dll">
      <PackagePath>clretwrc.dll</PackagePath>
      <Modified>2025-07-15T17:46:04.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\native\clrgc.dll">
      <PackagePath>clrgc.dll</PackagePath>
      <Modified>2025-07-15T17:43:54.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\native\clrjit.dll">
      <PackagePath>clrjit.dll</PackagePath>
      <Modified>2025-07-15T17:43:18.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\native\coreclr.dll">
      <PackagePath>coreclr.dll</PackagePath>
      <Modified>2025-07-15T17:40:42.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\native\createdump.exe">
      <PackagePath>createdump.exe</PackagePath>
      <Modified>2025-07-15T17:46:02.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\native\hostfxr.dll">
      <PackagePath>hostfxr.dll</PackagePath>
      <Modified>2025-07-15T17:56:48.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\native\hostpolicy.dll">
      <PackagePath>hostpolicy.dll</PackagePath>
      <Modified>2025-07-15T17:56:50.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\native\mscordaccore.dll">
      <PackagePath>mscordaccore.dll</PackagePath>
      <Modified>2025-07-15T17:48:44.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\native\mscordaccore_amd64_amd64_8.0.1925.36514.dll">
      <PackagePath>mscordaccore_amd64_amd64_8.0.1925.36514.dll</PackagePath>
      <Modified>2025-07-15T17:48:44.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\native\mscordbi.dll">
      <PackagePath>mscordbi.dll</PackagePath>
      <Modified>2025-07-15T17:49:34.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\native\mscorrc.dll">
      <PackagePath>mscorrc.dll</PackagePath>
      <Modified>2025-07-15T17:46:12.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\native\msquic.dll">
      <PackagePath>msquic.dll</PackagePath>
      <Modified>2025-03-03T22:23:18.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windows.sdk.net.ref\10.0.19041.57\lib\net8.0\Microsoft.Windows.SDK.NET.dll">
      <PackagePath>Microsoft.Windows.SDK.NET.dll</PackagePath>
      <Modified>2024-11-11T17:23:04.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windows.sdk.net.ref\10.0.19041.57\lib\net8.0\WinRT.Runtime.dll">
      <PackagePath>WinRT.Runtime.dll</PackagePath>
      <Modified>2024-11-11T17:23:04.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.ai\1.8.37\lib\net6.0-windows10.0.17763.0\Microsoft.Graphics.Imaging.Projection.dll">
      <PackagePath>Microsoft.Graphics.Imaging.Projection.dll</PackagePath>
      <Modified>2025-09-08T00:27:50.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.ai\1.8.37\lib\net6.0-windows10.0.17763.0\Microsoft.Windows.AI.ContentSafety.Projection.dll">
      <PackagePath>Microsoft.Windows.AI.ContentSafety.Projection.dll</PackagePath>
      <Modified>2025-09-08T00:27:50.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.ai\1.8.37\lib\net6.0-windows10.0.17763.0\Microsoft.Windows.AI.Foundation.Projection.dll">
      <PackagePath>Microsoft.Windows.AI.Foundation.Projection.dll</PackagePath>
      <Modified>2025-09-08T00:27:50.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.ai\1.8.37\lib\net6.0-windows10.0.17763.0\Microsoft.Windows.AI.Imaging.Projection.dll">
      <PackagePath>Microsoft.Windows.AI.Imaging.Projection.dll</PackagePath>
      <Modified>2025-09-08T00:27:50.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.ai\1.8.37\lib\net6.0-windows10.0.17763.0\Microsoft.Windows.AI.Projection.dll">
      <PackagePath>Microsoft.Windows.AI.Projection.dll</PackagePath>
      <Modified>2025-09-08T00:27:50.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.ai\1.8.37\lib\net6.0-windows10.0.17763.0\Microsoft.Windows.AI.Text.Projection.dll">
      <PackagePath>Microsoft.Windows.AI.Text.Projection.dll</PackagePath>
      <Modified>2025-09-08T00:27:52.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.foundation\1.8.250906002\lib\net6.0-windows10.0.17763.0\Microsoft.Security.Authentication.OAuth.Projection.dll">
      <PackagePath>Microsoft.Security.Authentication.OAuth.Projection.dll</PackagePath>
      <Modified>2025-09-06T23:02:10.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.foundation\1.8.250906002\lib\net6.0-windows10.0.17763.0\Microsoft.Windows.AppLifecycle.Projection.dll">
      <PackagePath>Microsoft.Windows.AppLifecycle.Projection.dll</PackagePath>
      <Modified>2025-09-06T23:02:12.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.foundation\1.8.250906002\lib\net6.0-windows10.0.17763.0\Microsoft.Windows.AppNotifications.Builder.Projection.dll">
      <PackagePath>Microsoft.Windows.AppNotifications.Builder.Projection.dll</PackagePath>
      <Modified>2025-09-06T23:02:12.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.foundation\1.8.250906002\lib\net6.0-windows10.0.17763.0\Microsoft.Windows.AppNotifications.Projection.dll">
      <PackagePath>Microsoft.Windows.AppNotifications.Projection.dll</PackagePath>
      <Modified>2025-09-06T23:02:12.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.foundation\1.8.250906002\lib\net6.0-windows10.0.17763.0\Microsoft.Windows.ApplicationModel.Background.Projection.dll">
      <PackagePath>Microsoft.Windows.ApplicationModel.Background.Projection.dll</PackagePath>
      <Modified>2025-09-06T23:02:12.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.foundation\1.8.250906002\lib\net6.0-windows10.0.17763.0\Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.dll">
      <PackagePath>Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.dll</PackagePath>
      <Modified>2025-09-06T23:02:12.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.foundation\1.8.250906002\lib\net6.0-windows10.0.17763.0\Microsoft.Windows.ApplicationModel.Resources.Projection.dll">
      <PackagePath>Microsoft.Windows.ApplicationModel.Resources.Projection.dll</PackagePath>
      <Modified>2025-09-06T23:02:42.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.foundation\1.8.250906002\lib\net6.0-windows10.0.17763.0\Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.dll">
      <PackagePath>Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.dll</PackagePath>
      <Modified>2025-09-06T23:02:12.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.foundation\1.8.250906002\lib\net6.0-windows10.0.17763.0\Microsoft.Windows.BadgeNotifications.Projection.dll">
      <PackagePath>Microsoft.Windows.BadgeNotifications.Projection.dll</PackagePath>
      <Modified>2025-09-06T23:02:12.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.foundation\1.8.250906002\lib\net6.0-windows10.0.17763.0\Microsoft.Windows.Foundation.Projection.dll">
      <PackagePath>Microsoft.Windows.Foundation.Projection.dll</PackagePath>
      <Modified>2025-09-06T23:02:12.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.foundation\1.8.250906002\lib\net6.0-windows10.0.17763.0\Microsoft.Windows.Management.Deployment.Projection.dll">
      <PackagePath>Microsoft.Windows.Management.Deployment.Projection.dll</PackagePath>
      <Modified>2025-09-06T23:02:12.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.foundation\1.8.250906002\lib\net6.0-windows10.0.17763.0\Microsoft.Windows.Media.Capture.Projection.dll">
      <PackagePath>Microsoft.Windows.Media.Capture.Projection.dll</PackagePath>
      <Modified>2025-09-06T23:02:12.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.foundation\1.8.250906002\lib\net6.0-windows10.0.17763.0\Microsoft.Windows.PushNotifications.Projection.dll">
      <PackagePath>Microsoft.Windows.PushNotifications.Projection.dll</PackagePath>
      <Modified>2025-09-06T23:02:12.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.foundation\1.8.250906002\lib\net6.0-windows10.0.17763.0\Microsoft.Windows.Security.AccessControl.Projection.dll">
      <PackagePath>Microsoft.Windows.Security.AccessControl.Projection.dll</PackagePath>
      <Modified>2025-09-06T23:02:12.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.foundation\1.8.250906002\lib\net6.0-windows10.0.17763.0\Microsoft.Windows.Storage.Pickers.Projection.dll">
      <PackagePath>Microsoft.Windows.Storage.Pickers.Projection.dll</PackagePath>
      <Modified>2025-09-06T23:02:12.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.foundation\1.8.250906002\lib\net6.0-windows10.0.17763.0\Microsoft.Windows.Storage.Projection.dll">
      <PackagePath>Microsoft.Windows.Storage.Projection.dll</PackagePath>
      <Modified>2025-09-06T23:02:12.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.foundation\1.8.250906002\lib\net6.0-windows10.0.17763.0\Microsoft.Windows.System.Power.Projection.dll">
      <PackagePath>Microsoft.Windows.System.Power.Projection.dll</PackagePath>
      <Modified>2025-09-06T23:02:12.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.foundation\1.8.250906002\lib\net6.0-windows10.0.17763.0\Microsoft.Windows.System.Projection.dll">
      <PackagePath>Microsoft.Windows.System.Projection.dll</PackagePath>
      <Modified>2025-09-06T23:02:12.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.foundation\1.8.250906002\lib\net6.0-windows10.0.17763.0\Microsoft.WindowsAppRuntime.Bootstrap.Net.dll">
      <PackagePath>Microsoft.WindowsAppRuntime.Bootstrap.Net.dll</PackagePath>
      <Modified>2025-09-06T23:02:14.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.interactiveexperiences\1.8.250906004\lib\net6.0-windows10.0.18362.0\Microsoft.InteractiveExperiences.Projection.dll">
      <PackagePath>Microsoft.InteractiveExperiences.Projection.dll</PackagePath>
      <Modified>2025-09-06T11:38:40.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.widgets\1.8.250904007\lib\net6.0-windows10.0.17763.0\Microsoft.Windows.Widgets.Projection.dll">
      <PackagePath>Microsoft.Windows.Widgets.Projection.dll</PackagePath>
      <Modified>2025-09-05T02:13:54.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.winui\1.8.250906003\lib\net6.0-windows10.0.17763.0\Microsoft.WinUI.dll">
      <PackagePath>Microsoft.WinUI.dll</PackagePath>
      <Modified>2025-09-07T02:47:34.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.web.webview2\1.0.3179.45\runtimes\win-x64\native\WebView2Loader.dll">
      <PackagePath>WebView2Loader.dll</PackagePath>
      <Modified>2025-03-30T09:04:32.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.foundation\1.8.250906002\runtimes\win-x64\native\Microsoft.Windows.ApplicationModel.Background.UniversalBGTask.dll">
      <PackagePath>Microsoft.Windows.ApplicationModel.Background.UniversalBGTask.dll</PackagePath>
      <Modified>2025-09-06T23:01:30.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.foundation\1.8.250906002\runtimes\win-x64\native\Microsoft.WindowsAppRuntime.Bootstrap.dll">
      <PackagePath>Microsoft.WindowsAppRuntime.Bootstrap.dll</PackagePath>
      <Modified>2025-09-06T23:01:30.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.web.webview2\1.0.3179.45\runtimes\win-x64\native_uap\Microsoft.Web.WebView2.Core.dll">
      <PackagePath>Microsoft.Web.WebView2.Core.dll</PackagePath>
      <Modified>2025-03-30T09:04:20.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.web.webview2\1.0.3179.45\lib_manual\net8.0-windows10.0.17763.0\Microsoft.Web.WebView2.Core.Projection.dll">
      <PackagePath>Microsoft.Web.WebView2.Core.Projection.dll</PackagePath>
      <Modified>2025-03-30T09:04:20.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\bin\x64\Debug\net8.0-windows10.0.19041.0\win-x64\PrestoIves.deps.json">
      <PackagePath>PrestoIves.deps.json</PackagePath>
      <Modified>2025-09-12T23:05:41.304</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\Assets\LockScreenLogo.scale-200.png">
      <PackagePath>Assets\LockScreenLogo.scale-200.png</PackagePath>
      <Modified>2025-09-08T06:34:12.425</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\Assets\Samples\1.jpg">
      <PackagePath>Assets\Samples\1.jpg</PackagePath>
      <Modified>2025-07-01T22:41:46.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\Assets\Samples\10.jpg">
      <PackagePath>Assets\Samples\10.jpg</PackagePath>
      <Modified>2025-07-01T22:41:46.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\Assets\Samples\11.jpg">
      <PackagePath>Assets\Samples\11.jpg</PackagePath>
      <Modified>2025-07-01T22:41:46.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\Assets\Samples\12.jpg">
      <PackagePath>Assets\Samples\12.jpg</PackagePath>
      <Modified>2025-07-01T22:41:46.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\Assets\Samples\13.jpg">
      <PackagePath>Assets\Samples\13.jpg</PackagePath>
      <Modified>2025-07-01T22:41:46.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\Assets\Samples\14.jpg">
      <PackagePath>Assets\Samples\14.jpg</PackagePath>
      <Modified>2025-07-01T22:41:46.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\Assets\Samples\15.jpg">
      <PackagePath>Assets\Samples\15.jpg</PackagePath>
      <Modified>2025-07-01T22:41:46.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\Assets\Samples\16.jpg">
      <PackagePath>Assets\Samples\16.jpg</PackagePath>
      <Modified>2025-07-01T22:41:46.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\Assets\Samples\17.jpg">
      <PackagePath>Assets\Samples\17.jpg</PackagePath>
      <Modified>2025-07-01T22:41:46.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\Assets\Samples\18.jpg">
      <PackagePath>Assets\Samples\18.jpg</PackagePath>
      <Modified>2025-07-01T22:41:46.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\Assets\Samples\19.jpg">
      <PackagePath>Assets\Samples\19.jpg</PackagePath>
      <Modified>2025-07-01T22:41:46.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\Assets\Samples\2.jpg">
      <PackagePath>Assets\Samples\2.jpg</PackagePath>
      <Modified>2025-07-01T22:41:46.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\Assets\Samples\20.jpg">
      <PackagePath>Assets\Samples\20.jpg</PackagePath>
      <Modified>2025-07-01T22:41:46.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\Assets\Samples\3.jpg">
      <PackagePath>Assets\Samples\3.jpg</PackagePath>
      <Modified>2025-07-01T22:41:46.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\Assets\Samples\4.jpg">
      <PackagePath>Assets\Samples\4.jpg</PackagePath>
      <Modified>2025-07-01T22:41:46.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\Assets\Samples\5.jpg">
      <PackagePath>Assets\Samples\5.jpg</PackagePath>
      <Modified>2025-07-01T22:41:46.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\Assets\Samples\6.jpg">
      <PackagePath>Assets\Samples\6.jpg</PackagePath>
      <Modified>2025-07-01T22:41:46.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\Assets\Samples\7.jpg">
      <PackagePath>Assets\Samples\7.jpg</PackagePath>
      <Modified>2025-07-01T22:41:46.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\Assets\Samples\8.jpg">
      <PackagePath>Assets\Samples\8.jpg</PackagePath>
      <Modified>2025-07-01T22:41:46.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\Assets\Samples\9.jpg">
      <PackagePath>Assets\Samples\9.jpg</PackagePath>
      <Modified>2025-07-01T22:41:46.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\Assets\SplashScreen.scale-200.png">
      <PackagePath>Assets\SplashScreen.scale-200.png</PackagePath>
      <Modified>2025-09-08T06:34:12.425</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\Assets\Square150x150Logo.scale-200.png">
      <PackagePath>Assets\Square150x150Logo.scale-200.png</PackagePath>
      <Modified>2025-09-08T06:34:12.425</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\Assets\Square44x44Logo.scale-200.png">
      <PackagePath>Assets\Square44x44Logo.scale-200.png</PackagePath>
      <Modified>2025-09-08T06:34:12.425</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\Assets\Square44x44Logo.targetsize-24_altform-unplated.png">
      <PackagePath>Assets\Square44x44Logo.targetsize-24_altform-unplated.png</PackagePath>
      <Modified>2025-09-08T06:34:12.425</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\Assets\StoreLogo.png">
      <PackagePath>Assets\StoreLogo.png</PackagePath>
      <Modified>2025-09-08T06:34:12.425</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\Assets\Wide310x150Logo.scale-200.png">
      <PackagePath>Assets\Wide310x150Logo.scale-200.png</PackagePath>
      <Modified>2025-09-08T06:34:12.425</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.web.webview2\1.0.3179.45\build\..\lib\Microsoft.Web.WebView2.Core.winmd">
      <PackagePath>Microsoft.Web.WebView2.Core.winmd</PackagePath>
      <Modified>2025-03-30T09:03:56.000</Modified>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\bin\x64\Debug\net8.0-windows10.0.19041.0\win-x64\resources.pri">
      <PackagePath>resources.pri</PackagePath>
      <Modified>2025-09-13T23:26:13.267</Modified>
    </AppxPackagedFile>
  </ItemGroup>
  <ItemGroup>
    <ResolvedSDKReference Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.runtime\1.8.250907003\buildTransitive\..\tools\MSIX\win10-x86\Microsoft.WindowsAppRuntime.1.8.msix">
      <Name>Microsoft.WindowsAppRuntime.1.8</Name>
      <Version>8000.616.304.0</Version>
      <Architecture>x86</Architecture>
      <FrameworkIdentity>Name = Microsoft.WindowsAppRuntime.1.8, MinVersion = 8000.616.304.0, Publisher = %27CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US%27</FrameworkIdentity>
      <AppxLocation>C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.runtime\1.8.250907003\buildTransitive\..\tools\MSIX\win10-x86\Microsoft.WindowsAppRuntime.1.8.msix</AppxLocation>
      <MoreInfo>
      </MoreInfo>
    </ResolvedSDKReference>
    <ResolvedSDKReference Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.runtime\1.8.250907003\buildTransitive\..\tools\MSIX\win10-x86\Microsoft.WindowsAppRuntime.1.8.msix">
      <Name>Microsoft.WindowsAppRuntime.1.8</Name>
      <Version>8000.616.304.0</Version>
      <Architecture>win32</Architecture>
      <FrameworkIdentity>Name = Microsoft.WindowsAppRuntime.1.8, MinVersion = 8000.616.304.0, Publisher = %27CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US%27</FrameworkIdentity>
      <AppxLocation>C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.runtime\1.8.250907003\buildTransitive\..\tools\MSIX\win10-x86\Microsoft.WindowsAppRuntime.1.8.msix</AppxLocation>
      <MoreInfo>
      </MoreInfo>
    </ResolvedSDKReference>
    <ResolvedSDKReference Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.runtime\1.8.250907003\buildTransitive\..\tools\MSIX\win10-x64\Microsoft.WindowsAppRuntime.1.8.msix">
      <Name>Microsoft.WindowsAppRuntime.1.8</Name>
      <Version>8000.616.304.0</Version>
      <Architecture>x64</Architecture>
      <FrameworkIdentity>Name = Microsoft.WindowsAppRuntime.1.8, MinVersion = 8000.616.304.0, Publisher = %27CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US%27</FrameworkIdentity>
      <AppxLocation>C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.runtime\1.8.250907003\buildTransitive\..\tools\MSIX\win10-x64\Microsoft.WindowsAppRuntime.1.8.msix</AppxLocation>
      <MoreInfo>
      </MoreInfo>
    </ResolvedSDKReference>
    <ResolvedSDKReference Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.runtime\1.8.250907003\buildTransitive\..\tools\MSIX\win10-arm64\Microsoft.WindowsAppRuntime.1.8.msix">
      <Name>Microsoft.WindowsAppRuntime.1.8</Name>
      <Version>8000.616.304.0</Version>
      <Architecture>arm64</Architecture>
      <FrameworkIdentity>Name = Microsoft.WindowsAppRuntime.1.8, MinVersion = 8000.616.304.0, Publisher = %27CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US%27</FrameworkIdentity>
      <AppxLocation>C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.runtime\1.8.250907003\buildTransitive\..\tools\MSIX\win10-arm64\Microsoft.WindowsAppRuntime.1.8.msix</AppxLocation>
      <MoreInfo>
      </MoreInfo>
    </ResolvedSDKReference>
  </ItemGroup>
</Project>