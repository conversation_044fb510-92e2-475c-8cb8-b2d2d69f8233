﻿<?xml version="1.0" encoding="utf-8"?>

<Package
  xmlns="http://schemas.microsoft.com/appx/manifest/foundation/windows10"
  xmlns:mp="http://schemas.microsoft.com/appx/2014/phone/manifest"
  xmlns:uap="http://schemas.microsoft.com/appx/manifest/uap/windows10"
  xmlns:com="http://schemas.microsoft.com/appx/manifest/com/windows10"
  xmlns:rescap="http://schemas.microsoft.com/appx/manifest/foundation/windows10/restrictedcapabilities"
  IgnorableNamespaces="uap rescap">

  <Identity
    Name="6b1ec254-6909-4115-a6f6-1133733eb38e"
    Publisher="CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US"
    Version="1.0.0.0" />

  <mp:PhoneIdentity PhoneProductId="bb526a1f-8e02-4523-b45e-e2ee91c4c65b" PhonePublisherId="00000000-0000-0000-0000-000000000000"/>

  <Properties>
    <DisplayName>BackgroundTaskBuilder</DisplayName>
    <PublisherDisplayName>Microsoft</PublisherDisplayName>
    <Logo>Assets\StoreLogo.png</Logo>
  </Properties>

  <Dependencies>
    <TargetDeviceFamily Name="Windows.Universal" MinVersion="10.0.17763.0" MaxVersionTested="10.0.19041.0" />
    <TargetDeviceFamily Name="Windows.Desktop" MinVersion="10.0.17763.0" MaxVersionTested="10.0.19041.0" />
  </Dependencies>

  <Resources>
    <Resource Language="x-generate"/>
  </Resources>

  <Applications>
    <Application Id="App"
      Executable="$targetnametoken$.exe"
      EntryPoint="$targetentrypoint$">
      <uap:VisualElements
        DisplayName="BackgroundTaskBuilder"
        Description="BackgroundTaskBuilder"
        BackgroundColor="transparent"
        Square150x150Logo="Assets\Square150x150Logo.png"
        Square44x44Logo="Assets\Square44x44Logo.png">
        <uap:DefaultTile Wide310x150Logo="Assets\Wide310x150Logo.png" />
        <uap:SplashScreen Image="Assets\SplashScreen.png" />
      </uap:VisualElements>
      <Extensions>
        <!-- Below Entry Point is to be mentioned in case of C# application for usage of WinAppSDK Background Task API -->
        <Extension Category="windows.backgroundTasks" EntryPoint="Microsoft.Windows.ApplicationModel.Background.UniversalBGTask.Task">
          <BackgroundTasks>
            <Task Type="general"/>
          </BackgroundTasks>
        </Extension>
        <com:Extension Category="windows.comServer">
          <com:ComServer>
            <!-- COM Server for the background task, LaunchAndActivationPermission is required to give permission
                 for backgroundtaskhost process to cocreate this COM component -->
            <com:ExeServer Executable="BackgroundTaskBuilder.exe" Arguments="-RegisterForBGTaskServer" DisplayName="BackgroundTask"
                              LaunchAndActivationPermission="O:PSG:BUD:(A;;11;;;IU)(A;;11;;;S-1-15-2-1)S:(ML;;NX;;;LW)">
              <com:Class Id="87654321-1234-1234-1234-1234567890AE" DisplayName="BackgroundTask" />
            </com:ExeServer>
          </com:ComServer>
        </com:Extension>
      </Extensions>
    </Application>
  </Applications>
  <Extensions>
    <Extension Category="windows.activatableClass.inProcessServer">
      <InProcessServer>
        <Path>Microsoft.Windows.ApplicationModel.Background.UniversalBGTask.dll</Path>
        <ActivatableClass ActivatableClassId="Microsoft.Windows.ApplicationModel.Background.UniversalBGTask.Task" ThreadingModel="both"/>
      </InProcessServer>
    </Extension>
  </Extensions>
  <Capabilities>
    <rescap:Capability Name="runFullTrust" />
  </Capabilities>
</Package>
