﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ApplicationDefinition Include="App.xaml" />
  </ItemGroup>
  <ItemGroup>
    <Page Include="MainWindow.xaml" />
    <Page Include="MainPage.xaml" />
    <Page Include="SettingsPage.xaml" />
    <Page Include="Scenario1_CustomControl.xaml" />
    <Page Include="Scenario2_UserControl.xaml" />
    <Page Include="Styles.xaml" />
  </ItemGroup>
  <ItemGroup>
    <Midl Include="Project.idl" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="pch.cpp" />
    <ClCompile Include="$(GeneratedFilesDir)module.g.cpp" />
    <ClCompile Include="SampleConfiguration.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="pch.h" />
    <ClInclude Include="SampleConfiguration.h" />
  </ItemGroup>
  <ItemGroup>
    <Image Include="Assets\Wide310x150Logo.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\logo.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\Square150x150Logo.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\Square44x44Logo.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\SplashScreen.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\windows-sdk.ico">
      <Filter>Assets</Filter>
    </Image>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Assets">
      <UniqueIdentifier>{$guid1}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <Manifest Include="app.manifest" />
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config" />
  </ItemGroup>
</Project>