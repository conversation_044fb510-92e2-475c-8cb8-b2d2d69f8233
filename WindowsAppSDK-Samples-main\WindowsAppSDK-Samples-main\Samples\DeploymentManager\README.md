---
page_type: sample
languages:
- csharp
- cpp
products:
- windows
- windows-app-sdk
name: Deployment Manager sample
description: Shows how to use the Windows App Runtime Deployment API
urlFragment: deployment
extendedZipContent:
- path: LICENSE
  target: LICENSE
---
# Deployment Manager sample

This sample demonstrates how to use the DeploymentManager class to ensure the WindowsAppRuntime is initialized and in a good state.

## Prerequisites

* See [System requirements for Windows app development](https://docs.microsoft.com/windows/apps/windows-app-sdk/system-requirements).
* Make sure that your development environment is set up correctly&mdash;see [Install tools for developing apps for Windows 10 and Windows 11](https://docs.microsoft.com/windows/apps/windows-app-sdk/set-up-your-development-environment).
* The C# sample requires Visual Studio 2022 and .NET 6.

## Building and running the sample

* Open the solution file (`.sln`) in Visual Studio.
* From Visual Studio, either **Start Without Debugging** (Ctrl+F5) or **Start Debugging** (F5).

## Related Links

- [Windows App SDK](https://docs.microsoft.com/windows/apps/windows-app-sdk/)
- [Deployment API](https://docs.microsoft.com/windows/windows-app-sdk/api/winrt/microsoft.windows.applicationmodel.windowsappruntime.deploymentmanager)
