﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="packages\Microsoft.WindowsAppSDK.1.7.250310001\build\native\Microsoft.WindowsAppSDK.props" Condition="Exists('packages\Microsoft.WindowsAppSDK.1.7.250310001\build\native\Microsoft.WindowsAppSDK.props')" />
  <Import Project="packages\Microsoft.Windows.SDK.BuildTools.10.0.22621.3233\build\Microsoft.Windows.SDK.BuildTools.props" Condition="Exists('packages\Microsoft.Windows.SDK.BuildTools.10.0.22621.3233\build\Microsoft.Windows.SDK.BuildTools.props')" />
  <Import Project="packages\Microsoft.Windows.CppWinRT.2.0.230706.1\build\native\Microsoft.Windows.CppWinRT.props" Condition="Exists('packages\Microsoft.Windows.CppWinRT.2.0.230706.1\build\native\Microsoft.Windows.CppWinRT.props')" />
  <PropertyGroup Label="Globals">
    <CppWinRTOptimized>true</CppWinRTOptimized>
    <CppWinRTRootNamespaceAutoMerge>true</CppWinRTRootNamespaceAutoMerge>
    <MinimalCoreWin>true</MinimalCoreWin>
    <ProjectGuid>{95109056-a832-4ffb-bf6c-d758de67adb4}</ProjectGuid>
    <ProjectName>UXFrameworksOnIslands</ProjectName>
    <RootNamespace>UXFrameworksOnIslands</RootNamespace>
    <!--
      $(TargetName) should be same as $(RootNamespace) so that the produced binaries (.exe/.pri/etc.)
      have a name that matches the .winmd
    -->
    <TargetName>$(RootNamespace)</TargetName>
    <DefaultLanguage>en-US</DefaultLanguage>
    <MinimumVisualStudioVersion>16.0</MinimumVisualStudioVersion>
    <AppContainerApplication>false</AppContainerApplication>
    <AppxPackage>true</AppxPackage>
    <ApplicationType>Windows Store</ApplicationType>
    <ApplicationTypeRevision>10.0</ApplicationTypeRevision>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
    <WindowsTargetPlatformMinVersion>10.0.17763.0</WindowsTargetPlatformMinVersion>
    <EnableMsixTooling>true</EnableMsixTooling>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|ARM64">
      <Configuration>Debug</Configuration>
      <Platform>ARM64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|ARM64">
      <Configuration>Release</Configuration>
      <Platform>ARM64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
    <DesktopCompatible>true</DesktopCompatible>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)'=='Debug'" Label="Configuration">
    <UseDebugLibraries>true</UseDebugLibraries>
    <LinkIncremental>true</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)'=='Release'" Label="Configuration">
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <ItemDefinitionGroup>
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>precomp.h</PrecompiledHeaderFile>
      <WarningLevel>Level4</WarningLevel>
      <TreatWarningAsError>true</TreatWarningAsError>
      <AdditionalOptions>%(AdditionalOptions) /bigobj</AdditionalOptions>
    </ClCompile>
    <Link>
      <AdditionalDependencies>gdi32.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)'=='Debug'">
    <ClCompile>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)'=='Release'">
    <ClCompile>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup Condition="'$(WindowsPackageType)'!='None' and Exists('Package.appxmanifest')">
    <AppxManifest Include="Package.appxmanifest">
      <SubType>Designer</SubType>
    </AppxManifest>
  </ItemGroup>
  <ItemGroup>
    <Manifest Include="app.manifest" />
  </ItemGroup>
  <ItemGroup>
    <Image Include="Assets\LockScreenLogo.scale-200.png" />
    <Image Include="Assets\SplashScreen.scale-200.png" />
    <Image Include="Assets\Square150x150Logo.scale-200.png" />
    <Image Include="Assets\Square44x44Logo.scale-200.png" />
    <Image Include="Assets\Square44x44Logo.targetsize-24_altform-unplated.png" />
    <Image Include="Assets\StoreLogo.png" />
    <Image Include="Assets\Wide310x150Logo.scale-200.png" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="AutomationBase.h" />
    <ClInclude Include="AutomationCallbackHandler.h" />
    <ClInclude Include="AutomationCallbackRevoker.h" />
    <ClInclude Include="AutomationElement.h" />
    <ClInclude Include="AutomationFragment.h" />
    <ClInclude Include="AutomationFragmentRoot.h" />
    <ClInclude Include="AutomationPeer.h" />
    <ClInclude Include="AutomationTree.h" />
    <ClInclude Include="CheckBox.h" />
    <ClInclude Include="ColorUtils.h" />
    <ClInclude Include="CompositionDeviceResource.h" />
    <ClInclude Include="D2DSprite.h" />
    <ClInclude Include="DXDevice.h" />
    <ClInclude Include="EventRevokers.h" />
    <ClInclude Include="FocusList.h" />
    <ClInclude Include="FocusManager.h" />
    <ClInclude Include="FrameDocker.h" />
    <ClInclude Include="HitTestContext.h" />
    <ClInclude Include="IFocusHost.h" />
    <ClInclude Include="IFrame.h" />
    <ClInclude Include="IFrameHost.h" />
    <ClInclude Include="LiftedFrame.h" />
    <ClInclude Include="Matrix2x2.h" />
    <ClInclude Include="NetUIFrame.h" />
    <ClInclude Include="Output.h" />
    <ClInclude Include="OutputResource.h" />
    <ClInclude Include="PopupFrame.h" />
    <ClInclude Include="precomp.h" />
    <ClInclude Include="PreTranslateHandler.h" />
    <ClInclude Include="ReactNativeFrame.h" />
    <ClInclude Include="RootFrame.h" />
    <ClInclude Include="SettingCollection.h" />
    <ClInclude Include="SystemFrame.h" />
    <ClInclude Include="TemplateHelpers.h" />
    <ClInclude Include="TextRenderer.h" />
    <ClInclude Include="TextVisual.h" />
    <ClInclude Include="TopLevelWindow.h" />
    <ClInclude Include="VisualTreeNode.h" />
    <ClInclude Include="VisualUtils.h" />
    <ClInclude Include="WebViewFrame.h" />
    <ClInclude Include="WinUIFrame.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="AutomationBase.cpp" />
    <ClCompile Include="AutomationElement.cpp" />
    <ClCompile Include="AutomationFragment.cpp" />
    <ClCompile Include="AutomationFragmentRoot.cpp" />
    <ClCompile Include="AutomationPeer.cpp" />
    <ClCompile Include="AutomationTree.cpp" />
    <ClCompile Include="CheckBox.cpp" />
    <ClCompile Include="CompositionDeviceResource.cpp" />
    <ClCompile Include="D2DSprite.cpp" />
    <ClCompile Include="DXDevice.cpp" />
    <ClCompile Include="FocusList.cpp" />
    <ClCompile Include="FocusManager.cpp" />
    <ClCompile Include="FrameDocker.cpp" />
    <ClCompile Include="HitTestContext.cpp" />
    <ClCompile Include="LiftedFrame.cpp" />
    <ClCompile Include="main.cpp" />
    <ClCompile Include="NetUIFrame.cpp" />
    <ClCompile Include="Output.cpp" />
    <ClCompile Include="OutputResource.cpp" />
    <ClCompile Include="PopupFrame.cpp" />
    <ClCompile Include="precomp.cpp">
      <PrecompiledHeader>Create</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="PreTranslateHandler.cpp" />
    <ClCompile Include="ReactNativeFrame.cpp" />
    <ClCompile Include="RootFrame.cpp" />
    <ClCompile Include="SettingCollection.cpp" />
    <ClCompile Include="SystemFrame.cpp" />
    <ClCompile Include="TextRenderer.cpp" />
    <ClCompile Include="TextVisual.cpp" />
    <ClCompile Include="TopLevelWindow.cpp" />
    <ClCompile Include="VisualTreeNode.cpp" />
    <ClCompile Include="VisualUtils.cpp" />
    <ClCompile Include="WebViewFrame.cpp" />
    <ClCompile Include="WinUIFrame.cpp" />
  </ItemGroup>
  <!--
    Defining the "Msix" ProjectCapability here allows the Single-project MSIX Packaging
    Tools extension to be activated for this project even if the Windows App SDK Nuget
    package has not yet been restored.
  -->
  <ItemGroup Condition="'$(DisableMsixProjectCapabilityAddedByProject)'!='true' and '$(EnableMsixTooling)'=='true'">
    <ProjectCapability Include="Msix" />
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config" />
  </ItemGroup>
  <!--
    Defining the "HasPackageAndPublishMenuAddedByProject" property here allows the Solution
    Explorer "Package and Publish" context menu entry to be enabled for this project even if
    the Windows App SDK Nuget package has not yet been restored.
  -->
  <PropertyGroup Condition="'$(DisableHasPackageAndPublishMenuAddedByProject)'!='true' and '$(EnableMsixTooling)'=='true'">
    <HasPackageAndPublishMenu>true</HasPackageAndPublishMenu>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
    <Import Project="packages\Microsoft.Windows.ImplementationLibrary.1.0.240803.1\build\native\Microsoft.Windows.ImplementationLibrary.targets" Condition="Exists('packages\Microsoft.Windows.ImplementationLibrary.1.0.240803.1\build\native\Microsoft.Windows.ImplementationLibrary.targets')" />
    <Import Project="packages\Microsoft.Windows.CppWinRT.2.0.230706.1\build\native\Microsoft.Windows.CppWinRT.targets" Condition="Exists('packages\Microsoft.Windows.CppWinRT.2.0.230706.1\build\native\Microsoft.Windows.CppWinRT.targets')" />
    <Import Project="packages\Microsoft.Windows.SDK.BuildTools.10.0.22621.3233\build\Microsoft.Windows.SDK.BuildTools.targets" Condition="Exists('packages\Microsoft.Windows.SDK.BuildTools.10.0.22621.3233\build\Microsoft.Windows.SDK.BuildTools.targets')" />
    <Import Project="packages\Microsoft.Web.WebView2.1.0.2903.40\build\native\Microsoft.Web.WebView2.targets" Condition="Exists('packages\Microsoft.Web.WebView2.1.0.2903.40\build\native\Microsoft.Web.WebView2.targets')" />
    <Import Project="packages\Microsoft.WindowsAppSDK.1.7.250310001\build\native\Microsoft.WindowsAppSDK.targets" Condition="Exists('packages\Microsoft.WindowsAppSDK.1.7.250310001\build\native\Microsoft.WindowsAppSDK.targets')" />
  </ImportGroup>
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('packages\Microsoft.Windows.ImplementationLibrary.1.0.240803.1\build\native\Microsoft.Windows.ImplementationLibrary.targets')" Text="$([System.String]::Format('$(ErrorText)', 'packages\Microsoft.Windows.ImplementationLibrary.1.0.240803.1\build\native\Microsoft.Windows.ImplementationLibrary.targets'))" />
    <Error Condition="!Exists('packages\Microsoft.Windows.CppWinRT.2.0.230706.1\build\native\Microsoft.Windows.CppWinRT.props')" Text="$([System.String]::Format('$(ErrorText)', 'packages\Microsoft.Windows.CppWinRT.2.0.230706.1\build\native\Microsoft.Windows.CppWinRT.props'))" />
    <Error Condition="!Exists('packages\Microsoft.Windows.CppWinRT.2.0.230706.1\build\native\Microsoft.Windows.CppWinRT.targets')" Text="$([System.String]::Format('$(ErrorText)', 'packages\Microsoft.Windows.CppWinRT.2.0.230706.1\build\native\Microsoft.Windows.CppWinRT.targets'))" />
    <Error Condition="!Exists('packages\Microsoft.Windows.SDK.BuildTools.10.0.22621.3233\build\Microsoft.Windows.SDK.BuildTools.props')" Text="$([System.String]::Format('$(ErrorText)', 'packages\Microsoft.Windows.SDK.BuildTools.10.0.22621.3233\build\Microsoft.Windows.SDK.BuildTools.props'))" />
    <Error Condition="!Exists('packages\Microsoft.Windows.SDK.BuildTools.10.0.22621.3233\build\Microsoft.Windows.SDK.BuildTools.targets')" Text="$([System.String]::Format('$(ErrorText)', 'packages\Microsoft.Windows.SDK.BuildTools.10.0.22621.3233\build\Microsoft.Windows.SDK.BuildTools.targets'))" />
    <Error Condition="!Exists('packages\Microsoft.Web.WebView2.1.0.2903.40\build\native\Microsoft.Web.WebView2.targets')" Text="$([System.String]::Format('$(ErrorText)', 'packages\Microsoft.Web.WebView2.1.0.2903.40\build\native\Microsoft.Web.WebView2.targets'))" />
    <Error Condition="!Exists('packages\Microsoft.WindowsAppSDK.1.7.250310001\build\native\Microsoft.WindowsAppSDK.props')" Text="$([System.String]::Format('$(ErrorText)', 'packages\Microsoft.WindowsAppSDK.1.7.250310001\build\native\Microsoft.WindowsAppSDK.props'))" />
    <Error Condition="!Exists('packages\Microsoft.WindowsAppSDK.1.7.250310001\build\native\Microsoft.WindowsAppSDK.targets')" Text="$([System.String]::Format('$(ErrorText)', 'packages\Microsoft.WindowsAppSDK.1.7.250310001\build\native\Microsoft.WindowsAppSDK.targets'))" />
  </Target>
</Project>
