﻿<!-- Copyright (c) Microsoft Corporation.
     Licensed under the MIT License. -->
<Page
    x:Class="Input.GestureRecognizer"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:Input"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d">

    <Grid x:Name="Grid" Margin="30, 0, 30, 0" Background="Transparent" RowDefinitions="100, 50, *">
        <TextBlock Grid.Row="0" Style="{StaticResource ScenarioDescriptionTextStyle}" TextWrapping="Wrap">
                    Demonstrates GestureRecognizer's Tap, Double Tap, Right Tap, Drag, and Hold events by
                        changing the rectangle's color when a different gesture is detected on the rectangle.
                        The rectangle's GestureRecognizer supports mouse, touch and pen for all events with
                        the exception of Drag, which only supports mouse and pen.
        </TextBlock>
        <StackPanel Grid.Row="1" Orientation="Horizontal" Spacing="10">
            <TextBlock
                Text="Gesture Recognizer Result:"
                Margin="0,5,0,0"/>
            <TextBlock 
                x:Name="GestureResultText"
                Margin="0,5,0,0"/>
        </StackPanel>

        <Rectangle 
            Grid.Row="2"
            x:Name="GestureRectangle"
            Fill="Red"
            Width="100"
            Height="100"
            PointerPressed="OnPointerPressed"
            PointerMoved="OnPointerMoved"
            PointerReleased="OnPointerReleased"
            PointerCanceled="OnPointerCanceled"/>
    </Grid>
</Page>
