// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.

#pragma once

#define WIN32_LEAN_AND_MEAN 
#include <windows.h>
#include <stdlib.h>
#include <malloc.h>
#include <memory.h>
#include <tchar.h>
#include <sstream>
#include "strsafe.h"

#include <winrt/Windows.Foundation.h>
#include <winrt/Windows.Foundation.Collections.h>
#include <appmodel.h>
#include <winrt/Microsoft.Windows.System.h>

#include <MddBootstrap.h>

