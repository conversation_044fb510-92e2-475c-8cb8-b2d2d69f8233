﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Machine>DESKTOP-N6K8R2J</Machine>
    <WindowsUser>ivane</WindowsUser>
    <TargetPlatformIdentifier>Windows</TargetPlatformIdentifier>
    <TargetOsVersion>10.0</TargetOsVersion>
    <TargetOsDescription>Windows 10.0</TargetOsDescription>
    <SolutionConfiguration>Debug|x64</SolutionConfiguration>
    <PackageArchitecture>x64</PackageArchitecture>
    <PackageIdentityName>d8982488-654b-4863-b09e-47f22e166952</PackageIdentityName>
    <PackageIdentityPublisher>CN=ivane</PackageIdentityPublisher>
    <IntermediateOutputPath>D:\Software3\PrestoIves\PrestoIves\PrestoIves\obj\x64\Debug\net8.0-windows10.0.19041.0\win-x64\</IntermediateOutputPath>
    <RemoteDeploymentType>CopyToDevice</RemoteDeploymentType>
    <PackageRegistrationPath></PackageRegistrationPath>
    <RemoveNonLayoutFiles>false</RemoveNonLayoutFiles>
    <DeployOptionalPackages>false</DeployOptionalPackages>
    <WindowsSdkPath>C:\Program Files %28x86%29\Windows Kits\10\</WindowsSdkPath>
    <LayoutDir>D:\Software3\PrestoIves\PrestoIves\PrestoIves\bin\x64\Debug\net8.0-windows10.0.19041.0\win-x64\AppX</LayoutDir>
  </PropertyGroup>
  <ItemGroup>
    <AppXManifest Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\bin\x64\Debug\net8.0-windows10.0.19041.0\win-x64\AppxManifest.xml">
      <PackagePath>AppxManifest.xml</PackagePath>
      <ReRegisterAppIfChanged>true</ReRegisterAppIfChanged>
    </AppXManifest>
  </ItemGroup>
  <ItemGroup>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\bin\x64\Debug\net8.0-windows10.0.19041.0\win-x64\PrestoIves.runtimeconfig.json">
      <PackagePath>PrestoIves.runtimeconfig.json</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\obj\x64\Debug\net8.0-windows10.0.19041.0\win-x64\apphost.exe">
      <PackagePath>PrestoIves.exe</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\obj\x64\Debug\net8.0-windows10.0.19041.0\win-x64\PrestoIves.dll">
      <PackagePath>PrestoIves.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\Microsoft.CSharp.dll">
      <PackagePath>Microsoft.CSharp.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\Microsoft.VisualBasic.Core.dll">
      <PackagePath>Microsoft.VisualBasic.Core.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\Microsoft.VisualBasic.dll">
      <PackagePath>Microsoft.VisualBasic.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\Microsoft.Win32.Primitives.dll">
      <PackagePath>Microsoft.Win32.Primitives.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\Microsoft.Win32.Registry.dll">
      <PackagePath>Microsoft.Win32.Registry.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.AppContext.dll">
      <PackagePath>System.AppContext.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Buffers.dll">
      <PackagePath>System.Buffers.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Collections.Concurrent.dll">
      <PackagePath>System.Collections.Concurrent.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Collections.Immutable.dll">
      <PackagePath>System.Collections.Immutable.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Collections.NonGeneric.dll">
      <PackagePath>System.Collections.NonGeneric.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Collections.Specialized.dll">
      <PackagePath>System.Collections.Specialized.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Collections.dll">
      <PackagePath>System.Collections.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.ComponentModel.Annotations.dll">
      <PackagePath>System.ComponentModel.Annotations.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.ComponentModel.DataAnnotations.dll">
      <PackagePath>System.ComponentModel.DataAnnotations.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.ComponentModel.EventBasedAsync.dll">
      <PackagePath>System.ComponentModel.EventBasedAsync.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.ComponentModel.Primitives.dll">
      <PackagePath>System.ComponentModel.Primitives.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.ComponentModel.TypeConverter.dll">
      <PackagePath>System.ComponentModel.TypeConverter.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.ComponentModel.dll">
      <PackagePath>System.ComponentModel.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Configuration.dll">
      <PackagePath>System.Configuration.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Console.dll">
      <PackagePath>System.Console.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Core.dll">
      <PackagePath>System.Core.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Data.Common.dll">
      <PackagePath>System.Data.Common.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Data.DataSetExtensions.dll">
      <PackagePath>System.Data.DataSetExtensions.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Data.dll">
      <PackagePath>System.Data.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Diagnostics.Contracts.dll">
      <PackagePath>System.Diagnostics.Contracts.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Diagnostics.Debug.dll">
      <PackagePath>System.Diagnostics.Debug.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Diagnostics.DiagnosticSource.dll">
      <PackagePath>System.Diagnostics.DiagnosticSource.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Diagnostics.FileVersionInfo.dll">
      <PackagePath>System.Diagnostics.FileVersionInfo.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Diagnostics.Process.dll">
      <PackagePath>System.Diagnostics.Process.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Diagnostics.StackTrace.dll">
      <PackagePath>System.Diagnostics.StackTrace.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Diagnostics.TextWriterTraceListener.dll">
      <PackagePath>System.Diagnostics.TextWriterTraceListener.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Diagnostics.Tools.dll">
      <PackagePath>System.Diagnostics.Tools.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Diagnostics.TraceSource.dll">
      <PackagePath>System.Diagnostics.TraceSource.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Diagnostics.Tracing.dll">
      <PackagePath>System.Diagnostics.Tracing.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Drawing.Primitives.dll">
      <PackagePath>System.Drawing.Primitives.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Drawing.dll">
      <PackagePath>System.Drawing.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Dynamic.Runtime.dll">
      <PackagePath>System.Dynamic.Runtime.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Formats.Asn1.dll">
      <PackagePath>System.Formats.Asn1.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Formats.Tar.dll">
      <PackagePath>System.Formats.Tar.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Globalization.Calendars.dll">
      <PackagePath>System.Globalization.Calendars.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Globalization.Extensions.dll">
      <PackagePath>System.Globalization.Extensions.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Globalization.dll">
      <PackagePath>System.Globalization.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.IO.Compression.Brotli.dll">
      <PackagePath>System.IO.Compression.Brotli.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.IO.Compression.FileSystem.dll">
      <PackagePath>System.IO.Compression.FileSystem.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.IO.Compression.ZipFile.dll">
      <PackagePath>System.IO.Compression.ZipFile.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.IO.Compression.dll">
      <PackagePath>System.IO.Compression.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.IO.FileSystem.AccessControl.dll">
      <PackagePath>System.IO.FileSystem.AccessControl.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.IO.FileSystem.DriveInfo.dll">
      <PackagePath>System.IO.FileSystem.DriveInfo.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.IO.FileSystem.Primitives.dll">
      <PackagePath>System.IO.FileSystem.Primitives.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.IO.FileSystem.Watcher.dll">
      <PackagePath>System.IO.FileSystem.Watcher.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.IO.FileSystem.dll">
      <PackagePath>System.IO.FileSystem.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.IO.IsolatedStorage.dll">
      <PackagePath>System.IO.IsolatedStorage.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.IO.MemoryMappedFiles.dll">
      <PackagePath>System.IO.MemoryMappedFiles.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.IO.Pipes.AccessControl.dll">
      <PackagePath>System.IO.Pipes.AccessControl.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.IO.Pipes.dll">
      <PackagePath>System.IO.Pipes.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.IO.UnmanagedMemoryStream.dll">
      <PackagePath>System.IO.UnmanagedMemoryStream.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.IO.dll">
      <PackagePath>System.IO.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Linq.Expressions.dll">
      <PackagePath>System.Linq.Expressions.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Linq.Parallel.dll">
      <PackagePath>System.Linq.Parallel.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Linq.Queryable.dll">
      <PackagePath>System.Linq.Queryable.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Linq.dll">
      <PackagePath>System.Linq.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Memory.dll">
      <PackagePath>System.Memory.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Net.Http.Json.dll">
      <PackagePath>System.Net.Http.Json.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Net.Http.dll">
      <PackagePath>System.Net.Http.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Net.HttpListener.dll">
      <PackagePath>System.Net.HttpListener.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Net.Mail.dll">
      <PackagePath>System.Net.Mail.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Net.NameResolution.dll">
      <PackagePath>System.Net.NameResolution.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Net.NetworkInformation.dll">
      <PackagePath>System.Net.NetworkInformation.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Net.Ping.dll">
      <PackagePath>System.Net.Ping.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Net.Primitives.dll">
      <PackagePath>System.Net.Primitives.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Net.Quic.dll">
      <PackagePath>System.Net.Quic.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Net.Requests.dll">
      <PackagePath>System.Net.Requests.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Net.Security.dll">
      <PackagePath>System.Net.Security.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Net.ServicePoint.dll">
      <PackagePath>System.Net.ServicePoint.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Net.Sockets.dll">
      <PackagePath>System.Net.Sockets.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Net.WebClient.dll">
      <PackagePath>System.Net.WebClient.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Net.WebHeaderCollection.dll">
      <PackagePath>System.Net.WebHeaderCollection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Net.WebProxy.dll">
      <PackagePath>System.Net.WebProxy.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Net.WebSockets.Client.dll">
      <PackagePath>System.Net.WebSockets.Client.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Net.WebSockets.dll">
      <PackagePath>System.Net.WebSockets.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Net.dll">
      <PackagePath>System.Net.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Numerics.Vectors.dll">
      <PackagePath>System.Numerics.Vectors.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Numerics.dll">
      <PackagePath>System.Numerics.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.ObjectModel.dll">
      <PackagePath>System.ObjectModel.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Private.CoreLib.dll">
      <PackagePath>System.Private.CoreLib.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Private.DataContractSerialization.dll">
      <PackagePath>System.Private.DataContractSerialization.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Private.Uri.dll">
      <PackagePath>System.Private.Uri.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Private.Xml.Linq.dll">
      <PackagePath>System.Private.Xml.Linq.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Private.Xml.dll">
      <PackagePath>System.Private.Xml.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Reflection.DispatchProxy.dll">
      <PackagePath>System.Reflection.DispatchProxy.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Reflection.Emit.ILGeneration.dll">
      <PackagePath>System.Reflection.Emit.ILGeneration.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Reflection.Emit.Lightweight.dll">
      <PackagePath>System.Reflection.Emit.Lightweight.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Reflection.Emit.dll">
      <PackagePath>System.Reflection.Emit.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Reflection.Extensions.dll">
      <PackagePath>System.Reflection.Extensions.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Reflection.Metadata.dll">
      <PackagePath>System.Reflection.Metadata.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Reflection.Primitives.dll">
      <PackagePath>System.Reflection.Primitives.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Reflection.TypeExtensions.dll">
      <PackagePath>System.Reflection.TypeExtensions.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Reflection.dll">
      <PackagePath>System.Reflection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Resources.Reader.dll">
      <PackagePath>System.Resources.Reader.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Resources.ResourceManager.dll">
      <PackagePath>System.Resources.ResourceManager.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Resources.Writer.dll">
      <PackagePath>System.Resources.Writer.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Runtime.CompilerServices.Unsafe.dll">
      <PackagePath>System.Runtime.CompilerServices.Unsafe.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Runtime.CompilerServices.VisualC.dll">
      <PackagePath>System.Runtime.CompilerServices.VisualC.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Runtime.Extensions.dll">
      <PackagePath>System.Runtime.Extensions.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Runtime.Handles.dll">
      <PackagePath>System.Runtime.Handles.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Runtime.InteropServices.JavaScript.dll">
      <PackagePath>System.Runtime.InteropServices.JavaScript.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Runtime.InteropServices.RuntimeInformation.dll">
      <PackagePath>System.Runtime.InteropServices.RuntimeInformation.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Runtime.InteropServices.dll">
      <PackagePath>System.Runtime.InteropServices.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Runtime.Intrinsics.dll">
      <PackagePath>System.Runtime.Intrinsics.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Runtime.Loader.dll">
      <PackagePath>System.Runtime.Loader.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Runtime.Numerics.dll">
      <PackagePath>System.Runtime.Numerics.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Runtime.Serialization.Formatters.dll">
      <PackagePath>System.Runtime.Serialization.Formatters.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Runtime.Serialization.Json.dll">
      <PackagePath>System.Runtime.Serialization.Json.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Runtime.Serialization.Primitives.dll">
      <PackagePath>System.Runtime.Serialization.Primitives.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Runtime.Serialization.Xml.dll">
      <PackagePath>System.Runtime.Serialization.Xml.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Runtime.Serialization.dll">
      <PackagePath>System.Runtime.Serialization.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Runtime.dll">
      <PackagePath>System.Runtime.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Security.AccessControl.dll">
      <PackagePath>System.Security.AccessControl.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Security.Claims.dll">
      <PackagePath>System.Security.Claims.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Security.Cryptography.Algorithms.dll">
      <PackagePath>System.Security.Cryptography.Algorithms.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Security.Cryptography.Cng.dll">
      <PackagePath>System.Security.Cryptography.Cng.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Security.Cryptography.Csp.dll">
      <PackagePath>System.Security.Cryptography.Csp.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Security.Cryptography.Encoding.dll">
      <PackagePath>System.Security.Cryptography.Encoding.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Security.Cryptography.OpenSsl.dll">
      <PackagePath>System.Security.Cryptography.OpenSsl.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Security.Cryptography.Primitives.dll">
      <PackagePath>System.Security.Cryptography.Primitives.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Security.Cryptography.X509Certificates.dll">
      <PackagePath>System.Security.Cryptography.X509Certificates.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Security.Cryptography.dll">
      <PackagePath>System.Security.Cryptography.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Security.Principal.Windows.dll">
      <PackagePath>System.Security.Principal.Windows.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Security.Principal.dll">
      <PackagePath>System.Security.Principal.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Security.SecureString.dll">
      <PackagePath>System.Security.SecureString.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Security.dll">
      <PackagePath>System.Security.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.ServiceModel.Web.dll">
      <PackagePath>System.ServiceModel.Web.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.ServiceProcess.dll">
      <PackagePath>System.ServiceProcess.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Text.Encoding.CodePages.dll">
      <PackagePath>System.Text.Encoding.CodePages.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Text.Encoding.Extensions.dll">
      <PackagePath>System.Text.Encoding.Extensions.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Text.Encoding.dll">
      <PackagePath>System.Text.Encoding.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Text.Encodings.Web.dll">
      <PackagePath>System.Text.Encodings.Web.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Text.Json.dll">
      <PackagePath>System.Text.Json.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Text.RegularExpressions.dll">
      <PackagePath>System.Text.RegularExpressions.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Threading.Channels.dll">
      <PackagePath>System.Threading.Channels.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Threading.Overlapped.dll">
      <PackagePath>System.Threading.Overlapped.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Threading.Tasks.Dataflow.dll">
      <PackagePath>System.Threading.Tasks.Dataflow.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Threading.Tasks.Extensions.dll">
      <PackagePath>System.Threading.Tasks.Extensions.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Threading.Tasks.Parallel.dll">
      <PackagePath>System.Threading.Tasks.Parallel.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Threading.Tasks.dll">
      <PackagePath>System.Threading.Tasks.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Threading.Thread.dll">
      <PackagePath>System.Threading.Thread.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Threading.ThreadPool.dll">
      <PackagePath>System.Threading.ThreadPool.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Threading.Timer.dll">
      <PackagePath>System.Threading.Timer.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Threading.dll">
      <PackagePath>System.Threading.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Transactions.Local.dll">
      <PackagePath>System.Transactions.Local.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Transactions.dll">
      <PackagePath>System.Transactions.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.ValueTuple.dll">
      <PackagePath>System.ValueTuple.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Web.HttpUtility.dll">
      <PackagePath>System.Web.HttpUtility.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Web.dll">
      <PackagePath>System.Web.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Windows.dll">
      <PackagePath>System.Windows.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Xml.Linq.dll">
      <PackagePath>System.Xml.Linq.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Xml.ReaderWriter.dll">
      <PackagePath>System.Xml.ReaderWriter.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Xml.Serialization.dll">
      <PackagePath>System.Xml.Serialization.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Xml.XDocument.dll">
      <PackagePath>System.Xml.XDocument.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Xml.XPath.XDocument.dll">
      <PackagePath>System.Xml.XPath.XDocument.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Xml.XPath.dll">
      <PackagePath>System.Xml.XPath.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Xml.XmlDocument.dll">
      <PackagePath>System.Xml.XmlDocument.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Xml.XmlSerializer.dll">
      <PackagePath>System.Xml.XmlSerializer.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.Xml.dll">
      <PackagePath>System.Xml.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\System.dll">
      <PackagePath>System.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\WindowsBase.dll">
      <PackagePath>WindowsBase.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\mscorlib.dll">
      <PackagePath>mscorlib.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\lib\net8.0\netstandard.dll">
      <PackagePath>netstandard.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\native\Microsoft.DiaSymReader.Native.amd64.dll">
      <PackagePath>Microsoft.DiaSymReader.Native.amd64.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\native\System.IO.Compression.Native.dll">
      <PackagePath>System.IO.Compression.Native.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\native\clretwrc.dll">
      <PackagePath>clretwrc.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\native\clrgc.dll">
      <PackagePath>clrgc.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\native\clrjit.dll">
      <PackagePath>clrjit.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\native\coreclr.dll">
      <PackagePath>coreclr.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\native\createdump.exe">
      <PackagePath>createdump.exe</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\native\hostfxr.dll">
      <PackagePath>hostfxr.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\native\hostpolicy.dll">
      <PackagePath>hostpolicy.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\native\mscordaccore.dll">
      <PackagePath>mscordaccore.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\native\mscordaccore_amd64_amd64_8.0.1925.36514.dll">
      <PackagePath>mscordaccore_amd64_amd64_8.0.1925.36514.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\native\mscordbi.dll">
      <PackagePath>mscordbi.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\native\mscorrc.dll">
      <PackagePath>mscorrc.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.runtime.win-x64\8.0.19\runtimes\win-x64\native\msquic.dll">
      <PackagePath>msquic.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windows.sdk.net.ref\10.0.19041.57\lib\net8.0\Microsoft.Windows.SDK.NET.dll">
      <PackagePath>Microsoft.Windows.SDK.NET.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windows.sdk.net.ref\10.0.19041.57\lib\net8.0\WinRT.Runtime.dll">
      <PackagePath>WinRT.Runtime.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.ai\1.8.37\lib\net6.0-windows10.0.17763.0\Microsoft.Graphics.Imaging.Projection.dll">
      <PackagePath>Microsoft.Graphics.Imaging.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.ai\1.8.37\lib\net6.0-windows10.0.17763.0\Microsoft.Windows.AI.ContentSafety.Projection.dll">
      <PackagePath>Microsoft.Windows.AI.ContentSafety.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.ai\1.8.37\lib\net6.0-windows10.0.17763.0\Microsoft.Windows.AI.Foundation.Projection.dll">
      <PackagePath>Microsoft.Windows.AI.Foundation.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.ai\1.8.37\lib\net6.0-windows10.0.17763.0\Microsoft.Windows.AI.Imaging.Projection.dll">
      <PackagePath>Microsoft.Windows.AI.Imaging.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.ai\1.8.37\lib\net6.0-windows10.0.17763.0\Microsoft.Windows.AI.Projection.dll">
      <PackagePath>Microsoft.Windows.AI.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.ai\1.8.37\lib\net6.0-windows10.0.17763.0\Microsoft.Windows.AI.Text.Projection.dll">
      <PackagePath>Microsoft.Windows.AI.Text.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.foundation\1.8.250906002\lib\net6.0-windows10.0.17763.0\Microsoft.Security.Authentication.OAuth.Projection.dll">
      <PackagePath>Microsoft.Security.Authentication.OAuth.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.foundation\1.8.250906002\lib\net6.0-windows10.0.17763.0\Microsoft.Windows.AppLifecycle.Projection.dll">
      <PackagePath>Microsoft.Windows.AppLifecycle.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.foundation\1.8.250906002\lib\net6.0-windows10.0.17763.0\Microsoft.Windows.AppNotifications.Builder.Projection.dll">
      <PackagePath>Microsoft.Windows.AppNotifications.Builder.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.foundation\1.8.250906002\lib\net6.0-windows10.0.17763.0\Microsoft.Windows.AppNotifications.Projection.dll">
      <PackagePath>Microsoft.Windows.AppNotifications.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.foundation\1.8.250906002\lib\net6.0-windows10.0.17763.0\Microsoft.Windows.ApplicationModel.Background.Projection.dll">
      <PackagePath>Microsoft.Windows.ApplicationModel.Background.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.foundation\1.8.250906002\lib\net6.0-windows10.0.17763.0\Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.dll">
      <PackagePath>Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.foundation\1.8.250906002\lib\net6.0-windows10.0.17763.0\Microsoft.Windows.ApplicationModel.Resources.Projection.dll">
      <PackagePath>Microsoft.Windows.ApplicationModel.Resources.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.foundation\1.8.250906002\lib\net6.0-windows10.0.17763.0\Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.dll">
      <PackagePath>Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.foundation\1.8.250906002\lib\net6.0-windows10.0.17763.0\Microsoft.Windows.BadgeNotifications.Projection.dll">
      <PackagePath>Microsoft.Windows.BadgeNotifications.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.foundation\1.8.250906002\lib\net6.0-windows10.0.17763.0\Microsoft.Windows.Foundation.Projection.dll">
      <PackagePath>Microsoft.Windows.Foundation.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.foundation\1.8.250906002\lib\net6.0-windows10.0.17763.0\Microsoft.Windows.Management.Deployment.Projection.dll">
      <PackagePath>Microsoft.Windows.Management.Deployment.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.foundation\1.8.250906002\lib\net6.0-windows10.0.17763.0\Microsoft.Windows.Media.Capture.Projection.dll">
      <PackagePath>Microsoft.Windows.Media.Capture.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.foundation\1.8.250906002\lib\net6.0-windows10.0.17763.0\Microsoft.Windows.PushNotifications.Projection.dll">
      <PackagePath>Microsoft.Windows.PushNotifications.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.foundation\1.8.250906002\lib\net6.0-windows10.0.17763.0\Microsoft.Windows.Security.AccessControl.Projection.dll">
      <PackagePath>Microsoft.Windows.Security.AccessControl.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.foundation\1.8.250906002\lib\net6.0-windows10.0.17763.0\Microsoft.Windows.Storage.Pickers.Projection.dll">
      <PackagePath>Microsoft.Windows.Storage.Pickers.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.foundation\1.8.250906002\lib\net6.0-windows10.0.17763.0\Microsoft.Windows.Storage.Projection.dll">
      <PackagePath>Microsoft.Windows.Storage.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.foundation\1.8.250906002\lib\net6.0-windows10.0.17763.0\Microsoft.Windows.System.Power.Projection.dll">
      <PackagePath>Microsoft.Windows.System.Power.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.foundation\1.8.250906002\lib\net6.0-windows10.0.17763.0\Microsoft.Windows.System.Projection.dll">
      <PackagePath>Microsoft.Windows.System.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.foundation\1.8.250906002\lib\net6.0-windows10.0.17763.0\Microsoft.WindowsAppRuntime.Bootstrap.Net.dll">
      <PackagePath>Microsoft.WindowsAppRuntime.Bootstrap.Net.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.interactiveexperiences\1.8.250906004\lib\net6.0-windows10.0.18362.0\Microsoft.InteractiveExperiences.Projection.dll">
      <PackagePath>Microsoft.InteractiveExperiences.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.widgets\1.8.250904007\lib\net6.0-windows10.0.17763.0\Microsoft.Windows.Widgets.Projection.dll">
      <PackagePath>Microsoft.Windows.Widgets.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.winui\1.8.250906003\lib\net6.0-windows10.0.17763.0\Microsoft.WinUI.dll">
      <PackagePath>Microsoft.WinUI.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.web.webview2\1.0.3179.45\runtimes\win-x64\native\WebView2Loader.dll">
      <PackagePath>WebView2Loader.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.foundation\1.8.250906002\runtimes\win-x64\native\Microsoft.Windows.ApplicationModel.Background.UniversalBGTask.dll">
      <PackagePath>Microsoft.Windows.ApplicationModel.Background.UniversalBGTask.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.foundation\1.8.250906002\runtimes\win-x64\native\Microsoft.WindowsAppRuntime.Bootstrap.dll">
      <PackagePath>Microsoft.WindowsAppRuntime.Bootstrap.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.web.webview2\1.0.3179.45\runtimes\win-x64\native_uap\Microsoft.Web.WebView2.Core.dll">
      <PackagePath>Microsoft.Web.WebView2.Core.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.web.webview2\1.0.3179.45\lib_manual\net8.0-windows10.0.17763.0\Microsoft.Web.WebView2.Core.Projection.dll">
      <PackagePath>Microsoft.Web.WebView2.Core.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\bin\x64\Debug\net8.0-windows10.0.19041.0\win-x64\PrestoIves.deps.json">
      <PackagePath>PrestoIves.deps.json</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\Assets\LockScreenLogo.scale-200.png">
      <PackagePath>Assets\LockScreenLogo.scale-200.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\Assets\Samples\1.jpg">
      <PackagePath>Assets\Samples\1.jpg</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\Assets\Samples\10.jpg">
      <PackagePath>Assets\Samples\10.jpg</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\Assets\Samples\11.jpg">
      <PackagePath>Assets\Samples\11.jpg</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\Assets\Samples\12.jpg">
      <PackagePath>Assets\Samples\12.jpg</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\Assets\Samples\13.jpg">
      <PackagePath>Assets\Samples\13.jpg</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\Assets\Samples\14.jpg">
      <PackagePath>Assets\Samples\14.jpg</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\Assets\Samples\15.jpg">
      <PackagePath>Assets\Samples\15.jpg</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\Assets\Samples\16.jpg">
      <PackagePath>Assets\Samples\16.jpg</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\Assets\Samples\17.jpg">
      <PackagePath>Assets\Samples\17.jpg</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\Assets\Samples\18.jpg">
      <PackagePath>Assets\Samples\18.jpg</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\Assets\Samples\19.jpg">
      <PackagePath>Assets\Samples\19.jpg</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\Assets\Samples\2.jpg">
      <PackagePath>Assets\Samples\2.jpg</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\Assets\Samples\20.jpg">
      <PackagePath>Assets\Samples\20.jpg</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\Assets\Samples\3.jpg">
      <PackagePath>Assets\Samples\3.jpg</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\Assets\Samples\4.jpg">
      <PackagePath>Assets\Samples\4.jpg</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\Assets\Samples\5.jpg">
      <PackagePath>Assets\Samples\5.jpg</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\Assets\Samples\6.jpg">
      <PackagePath>Assets\Samples\6.jpg</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\Assets\Samples\7.jpg">
      <PackagePath>Assets\Samples\7.jpg</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\Assets\Samples\8.jpg">
      <PackagePath>Assets\Samples\8.jpg</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\Assets\Samples\9.jpg">
      <PackagePath>Assets\Samples\9.jpg</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\Assets\SplashScreen.scale-200.png">
      <PackagePath>Assets\SplashScreen.scale-200.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\Assets\Square150x150Logo.scale-200.png">
      <PackagePath>Assets\Square150x150Logo.scale-200.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\Assets\Square44x44Logo.scale-200.png">
      <PackagePath>Assets\Square44x44Logo.scale-200.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\Assets\Square44x44Logo.targetsize-24_altform-unplated.png">
      <PackagePath>Assets\Square44x44Logo.targetsize-24_altform-unplated.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\Assets\StoreLogo.png">
      <PackagePath>Assets\StoreLogo.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\Assets\Wide310x150Logo.scale-200.png">
      <PackagePath>Assets\Wide310x150Logo.scale-200.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.web.webview2\1.0.3179.45\build\..\lib\Microsoft.Web.WebView2.Core.winmd">
      <PackagePath>Microsoft.Web.WebView2.Core.winmd</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Software3\PrestoIves\PrestoIves\PrestoIves\bin\x64\Debug\net8.0-windows10.0.19041.0\win-x64\resources.pri">
      <PackagePath>resources.pri</PackagePath>
    </AppxPackagedFile>
  </ItemGroup>
  <ItemGroup>
    <ResolvedSDKReference Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.runtime\1.8.250907003\buildTransitive\..\tools\MSIX\win10-x86\Microsoft.WindowsAppRuntime.1.8.msix">
      <Name>Microsoft.WindowsAppRuntime.1.8</Name>
      <Version>8000.616.304.0</Version>
      <Architecture>x86</Architecture>
      <FrameworkIdentity>Name = Microsoft.WindowsAppRuntime.1.8, MinVersion = 8000.616.304.0, Publisher = %27CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US%27</FrameworkIdentity>
      <AppxLocation>C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.runtime\1.8.250907003\buildTransitive\..\tools\MSIX\win10-x86\Microsoft.WindowsAppRuntime.1.8.msix</AppxLocation>
      <MoreInfo></MoreInfo>
    </ResolvedSDKReference>
    <ResolvedSDKReference Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.runtime\1.8.250907003\buildTransitive\..\tools\MSIX\win10-x86\Microsoft.WindowsAppRuntime.1.8.msix">
      <Name>Microsoft.WindowsAppRuntime.1.8</Name>
      <Version>8000.616.304.0</Version>
      <Architecture>win32</Architecture>
      <FrameworkIdentity>Name = Microsoft.WindowsAppRuntime.1.8, MinVersion = 8000.616.304.0, Publisher = %27CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US%27</FrameworkIdentity>
      <AppxLocation>C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.runtime\1.8.250907003\buildTransitive\..\tools\MSIX\win10-x86\Microsoft.WindowsAppRuntime.1.8.msix</AppxLocation>
      <MoreInfo></MoreInfo>
    </ResolvedSDKReference>
    <ResolvedSDKReference Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.runtime\1.8.250907003\buildTransitive\..\tools\MSIX\win10-x64\Microsoft.WindowsAppRuntime.1.8.msix">
      <Name>Microsoft.WindowsAppRuntime.1.8</Name>
      <Version>8000.616.304.0</Version>
      <Architecture>x64</Architecture>
      <FrameworkIdentity>Name = Microsoft.WindowsAppRuntime.1.8, MinVersion = 8000.616.304.0, Publisher = %27CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US%27</FrameworkIdentity>
      <AppxLocation>C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.runtime\1.8.250907003\buildTransitive\..\tools\MSIX\win10-x64\Microsoft.WindowsAppRuntime.1.8.msix</AppxLocation>
      <MoreInfo></MoreInfo>
    </ResolvedSDKReference>
    <ResolvedSDKReference Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.runtime\1.8.250907003\buildTransitive\..\tools\MSIX\win10-arm64\Microsoft.WindowsAppRuntime.1.8.msix">
      <Name>Microsoft.WindowsAppRuntime.1.8</Name>
      <Version>8000.616.304.0</Version>
      <Architecture>arm64</Architecture>
      <FrameworkIdentity>Name = Microsoft.WindowsAppRuntime.1.8, MinVersion = 8000.616.304.0, Publisher = %27CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US%27</FrameworkIdentity>
      <AppxLocation>C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk.runtime\1.8.250907003\buildTransitive\..\tools\MSIX\win10-arm64\Microsoft.WindowsAppRuntime.1.8.msix</AppxLocation>
      <MoreInfo></MoreInfo>
    </ResolvedSDKReference>
  </ItemGroup>
</Project>
