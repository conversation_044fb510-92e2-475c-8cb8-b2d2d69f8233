{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0/win-x64", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {}, ".NETCoreApp,Version=v8.0/win-x64": {"PrestoIves/1.0.0": {"dependencies": {"Microsoft.Windows.SDK.BuildTools": "10.0.26100.4948", "Microsoft.WindowsAppSDK": "1.8.250907003", "Microsoft.Web.WebView2.Core.Projection": "1.0.3179.45", "runtimepack.Microsoft.NETCore.App.Runtime.win-x64": "8.0.19", "runtimepack.Microsoft.Windows.SDK.NET.Ref": "10.0.19041.57"}, "runtime": {"PrestoIves.dll": {}}}, "runtimepack.Microsoft.NETCore.App.Runtime.win-x64/8.0.19": {"runtime": {"Microsoft.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "Microsoft.VisualBasic.Core.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.1925.36514"}, "Microsoft.VisualBasic.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "8.0.1925.36514"}, "Microsoft.Win32.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "Microsoft.Win32.Registry.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.AppContext.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Buffers.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Collections.Concurrent.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Collections.Immutable.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Collections.NonGeneric.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Collections.Specialized.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Collections.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.ComponentModel.Annotations.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.ComponentModel.DataAnnotations.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.ComponentModel.EventBasedAsync.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.ComponentModel.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.ComponentModel.TypeConverter.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.ComponentModel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Console.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Core.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Data.Common.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Data.DataSetExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Data.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Diagnostics.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Diagnostics.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Diagnostics.FileVersionInfo.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Diagnostics.Process.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Diagnostics.StackTrace.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Diagnostics.TextWriterTraceListener.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Diagnostics.Tools.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Diagnostics.TraceSource.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Diagnostics.Tracing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Drawing.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Drawing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Dynamic.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Formats.Asn1.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Formats.Tar.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Globalization.Calendars.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Globalization.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Globalization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.IO.Compression.Brotli.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.IO.Compression.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.IO.Compression.ZipFile.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.IO.Compression.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.IO.FileSystem.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.IO.FileSystem.DriveInfo.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.IO.FileSystem.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.IO.FileSystem.Watcher.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.IO.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.IO.IsolatedStorage.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.IO.MemoryMappedFiles.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.IO.Pipes.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.IO.Pipes.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.IO.UnmanagedMemoryStream.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.IO.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Linq.Expressions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Linq.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Linq.Queryable.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Net.Http.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Net.Http.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Net.HttpListener.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Net.Mail.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Net.NameResolution.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Net.NetworkInformation.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Net.Ping.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Net.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Net.Quic.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Net.Requests.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Net.Security.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Net.ServicePoint.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Net.Sockets.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Net.WebClient.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Net.WebHeaderCollection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Net.WebProxy.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Net.WebSockets.Client.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Net.WebSockets.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Net.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Numerics.Vectors.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.ObjectModel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Private.CoreLib.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Private.DataContractSerialization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Private.Uri.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Private.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Private.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Reflection.DispatchProxy.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Reflection.Emit.ILGeneration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Reflection.Emit.Lightweight.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Reflection.Emit.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Reflection.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Reflection.Metadata.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Reflection.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Reflection.TypeExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Reflection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Resources.Reader.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Resources.ResourceManager.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Resources.Writer.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Runtime.CompilerServices.Unsafe.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Runtime.CompilerServices.VisualC.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Runtime.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Runtime.Handles.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Runtime.InteropServices.JavaScript.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Runtime.InteropServices.RuntimeInformation.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Runtime.InteropServices.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Runtime.Intrinsics.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Runtime.Loader.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Runtime.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Runtime.Serialization.Formatters.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Runtime.Serialization.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Runtime.Serialization.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Runtime.Serialization.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Runtime.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Security.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Security.Claims.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Security.Cryptography.Algorithms.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Security.Cryptography.Cng.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Security.Cryptography.Csp.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Security.Cryptography.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Security.Cryptography.OpenSsl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Security.Cryptography.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Security.Cryptography.X509Certificates.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Security.Cryptography.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Security.Principal.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Security.Principal.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Security.SecureString.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Security.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.ServiceModel.Web.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.ServiceProcess.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Text.Encoding.CodePages.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Text.Encoding.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Text.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Text.RegularExpressions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Threading.Channels.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Threading.Overlapped.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Threading.Tasks.Dataflow.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Threading.Tasks.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Threading.Tasks.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Threading.Tasks.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Threading.Thread.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Threading.ThreadPool.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Threading.Timer.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Threading.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Transactions.Local.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Transactions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.ValueTuple.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Web.HttpUtility.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Web.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Xml.ReaderWriter.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Xml.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Xml.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Xml.XPath.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Xml.XPath.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Xml.XmlDocument.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Xml.XmlSerializer.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "System.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "WindowsBase.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "mscorlib.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}, "netstandard.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1925.36514"}}, "native": {"Microsoft.DiaSymReader.Native.amd64.dll": {"fileVersion": "14.42.34436.0"}, "System.IO.Compression.Native.dll": {"fileVersion": "8.0.1925.36514"}, "clretwrc.dll": {"fileVersion": "8.0.1925.36514"}, "clrgc.dll": {"fileVersion": "8.0.1925.36514"}, "clrjit.dll": {"fileVersion": "8.0.1925.36514"}, "coreclr.dll": {"fileVersion": "8.0.1925.36514"}, "createdump.exe": {"fileVersion": "8.0.1925.36514"}, "hostfxr.dll": {"fileVersion": "8.0.1925.36514"}, "hostpolicy.dll": {"fileVersion": "8.0.1925.36514"}, "mscordaccore.dll": {"fileVersion": "8.0.1925.36514"}, "mscordaccore_amd64_amd64_8.0.1925.36514.dll": {"fileVersion": "8.0.1925.36514"}, "mscordbi.dll": {"fileVersion": "8.0.1925.36514"}, "mscorrc.dll": {"fileVersion": "8.0.1925.36514"}, "msquic.dll": {"fileVersion": "*******"}}}, "runtimepack.Microsoft.Windows.SDK.NET.Ref/10.0.19041.57": {"runtime": {"Microsoft.Windows.SDK.NET.dll": {"assemblyVersion": "10.0.19041.38", "fileVersion": "10.0.19041.55"}, "WinRT.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.48161"}}}, "Microsoft.Web.WebView2/1.0.3179.45": {"native": {"runtimes/win-x64/native/WebView2Loader.dll": {"fileVersion": "1.0.3179.45"}}}, "Microsoft.Windows.SDK.BuildTools/10.0.26100.4948": {}, "Microsoft.Windows.SDK.BuildTools.MSIX/1.7.20250829.1": {}, "Microsoft.WindowsAppSDK/1.8.250907003": {"dependencies": {"Microsoft.WindowsAppSDK.AI": "1.8.37", "Microsoft.WindowsAppSDK.Base": "1.8.250831001", "Microsoft.WindowsAppSDK.DWrite": "1.8.25090401", "Microsoft.WindowsAppSDK.Foundation": "1.8.250906002", "Microsoft.WindowsAppSDK.InteractiveExperiences": "1.8.250906004", "Microsoft.WindowsAppSDK.Runtime": "1.8.250907003", "Microsoft.WindowsAppSDK.Widgets": "1.8.250904007", "Microsoft.WindowsAppSDK.WinUI": "1.8.250906003"}}, "Microsoft.WindowsAppSDK.AI/1.8.37": {"dependencies": {"Microsoft.WindowsAppSDK.Base": "1.8.250831001", "Microsoft.WindowsAppSDK.Foundation": "1.8.250906002"}, "runtime": {"lib/net6.0-windows10.0.17763.0/Microsoft.Graphics.Imaging.Projection.dll": {"assemblyVersion": "1.8.36.20617", "fileVersion": "1.8.36.20617"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.ContentSafety.Projection.dll": {"assemblyVersion": "1.8.36.20617", "fileVersion": "1.8.36.20617"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.Foundation.Projection.dll": {"assemblyVersion": "1.8.36.20617", "fileVersion": "1.8.36.20617"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.Imaging.Projection.dll": {"assemblyVersion": "1.8.36.20617", "fileVersion": "1.8.36.20617"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.Projection.dll": {"assemblyVersion": "1.8.36.20617", "fileVersion": "1.8.36.20617"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.Text.Projection.dll": {"assemblyVersion": "1.8.36.20617", "fileVersion": "1.8.36.20617"}}}, "Microsoft.WindowsAppSDK.Base/1.8.250831001": {"dependencies": {"Microsoft.Windows.SDK.BuildTools": "10.0.26100.4948", "Microsoft.Windows.SDK.BuildTools.MSIX": "1.7.20250829.1"}}, "Microsoft.WindowsAppSDK.DWrite/1.8.25090401": {"dependencies": {"Microsoft.WindowsAppSDK.Base": "1.8.250831001"}}, "Microsoft.WindowsAppSDK.Foundation/1.8.250906002": {"dependencies": {"Microsoft.WindowsAppSDK.Base": "1.8.250831001", "Microsoft.WindowsAppSDK.InteractiveExperiences": "1.8.250906004"}, "runtime": {"lib/net6.0-windows10.0.17763.0/Microsoft.Security.Authentication.OAuth.Projection.dll": {"assemblyVersion": "1.8.0.0", "fileVersion": "1.8.0.0"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppLifecycle.Projection.dll": {"assemblyVersion": "1.8.0.0", "fileVersion": "1.8.0.0"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppNotifications.Builder.Projection.dll": {"assemblyVersion": "1.8.0.0", "fileVersion": "1.8.0.0"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppNotifications.Projection.dll": {"assemblyVersion": "1.8.0.0", "fileVersion": "1.8.0.0"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.Background.Projection.dll": {"assemblyVersion": "1.8.0.0", "fileVersion": "1.8.0.0"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.dll": {"assemblyVersion": "1.8.0.0", "fileVersion": "1.8.0.0"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.Resources.Projection.dll": {"assemblyVersion": "1.8.0.0", "fileVersion": "1.8.0.0"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.dll": {"assemblyVersion": "1.8.0.0", "fileVersion": "1.8.0.0"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.BadgeNotifications.Projection.dll": {"assemblyVersion": "1.8.0.0", "fileVersion": "1.8.0.0"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Foundation.Projection.dll": {"assemblyVersion": "1.8.0.0", "fileVersion": "1.8.0.0"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Management.Deployment.Projection.dll": {"assemblyVersion": "1.8.0.0", "fileVersion": "1.8.0.0"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Media.Capture.Projection.dll": {"assemblyVersion": "1.8.0.0", "fileVersion": "1.8.0.0"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.PushNotifications.Projection.dll": {"assemblyVersion": "1.8.0.0", "fileVersion": "1.8.0.0"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Security.AccessControl.Projection.dll": {"assemblyVersion": "1.8.0.0", "fileVersion": "1.8.0.0"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Storage.Pickers.Projection.dll": {"assemblyVersion": "1.8.0.0", "fileVersion": "1.8.0.0"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Storage.Projection.dll": {"assemblyVersion": "1.8.0.0", "fileVersion": "1.8.0.0"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.System.Power.Projection.dll": {"assemblyVersion": "1.8.0.0", "fileVersion": "1.8.0.0"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.System.Projection.dll": {"assemblyVersion": "1.8.0.0", "fileVersion": "1.8.0.0"}, "lib/net6.0-windows10.0.17763.0/Microsoft.WindowsAppRuntime.Bootstrap.Net.dll": {"assemblyVersion": "1.8.0.0", "fileVersion": "1.8.0.0"}}, "native": {"runtimes/win-x64/native/Microsoft.Windows.ApplicationModel.Background.UniversalBGTask.dll": {"fileVersion": "1.8.0.0"}, "runtimes/win-x64/native/Microsoft.WindowsAppRuntime.Bootstrap.dll": {"fileVersion": "1.8.0.0"}}}, "Microsoft.WindowsAppSDK.InteractiveExperiences/1.8.250906004": {"dependencies": {"Microsoft.WindowsAppSDK.Base": "1.8.250831001"}, "runtime": {"lib/net6.0-windows10.0.18362.0/Microsoft.InteractiveExperiences.Projection.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.27108.1004"}}}, "Microsoft.WindowsAppSDK.Runtime/1.8.250907003": {"dependencies": {"Microsoft.WindowsAppSDK.Base": "1.8.250831001"}}, "Microsoft.WindowsAppSDK.Widgets/1.8.250904007": {"dependencies": {"Microsoft.WindowsAppSDK.Base": "1.8.250831001"}, "runtime": {"lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Widgets.Projection.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "Microsoft.WindowsAppSDK.WinUI/1.8.250906003": {"dependencies": {"Microsoft.Web.WebView2": "1.0.3179.45", "Microsoft.WindowsAppSDK.Base": "1.8.250831001", "Microsoft.WindowsAppSDK.Foundation": "1.8.250906002", "Microsoft.WindowsAppSDK.InteractiveExperiences": "1.8.250906004"}, "runtime": {"lib/net6.0-windows10.0.17763.0/Microsoft.WinUI.dll": {"assemblyVersion": "3.0.0.0", "fileVersion": "3.0.0.2509"}}}, "Microsoft.Web.WebView2.Core.Projection/1.0.3179.45": {"runtime": {"Microsoft.Web.WebView2.Core.Projection.dll": {"assemblyVersion": "1.0.3179.45", "fileVersion": "1.0.3179.45"}}}}}, "libraries": {"PrestoIves/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.NETCore.App.Runtime.win-x64/8.0.19": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.Windows.SDK.NET.Ref/10.0.19041.57": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "Microsoft.Web.WebView2/1.0.3179.45": {"type": "package", "serviceable": true, "sha512": "sha512-3pokSH5CnN0G6rGhGFo1y87inxYhNxBQ2Vdf0wlvBj99KHxQJormjDACmqRnFeUsmuNFIhWwfAL1ztq7wD5qRA==", "path": "microsoft.web.webview2/1.0.3179.45", "hashPath": "microsoft.web.webview2.1.0.3179.45.nupkg.sha512"}, "Microsoft.Windows.SDK.BuildTools/10.0.26100.4948": {"type": "package", "serviceable": true, "sha512": "sha512-o0T4CVaumDjPNNijKiM7p25vHKdyKqYvaVVLgQO02KTOoUDlgMYJVUQAXn1IG0G9/ZsdZ+bdgWxgQsrO/b37qw==", "path": "microsoft.windows.sdk.buildtools/10.0.26100.4948", "hashPath": "microsoft.windows.sdk.buildtools.10.0.26100.4948.nupkg.sha512"}, "Microsoft.Windows.SDK.BuildTools.MSIX/1.7.20250829.1": {"type": "package", "serviceable": true, "sha512": "sha512-IMdvRmCIZnBS5GkYnv0po1bcx6U1OF39pqA4TphQ9evDzpCRoSE19/PkDvlUNNrBavTsLIEJgd/TAIFner75ow==", "path": "microsoft.windows.sdk.buildtools.msix/1.7.20250829.1", "hashPath": "microsoft.windows.sdk.buildtools.msix.1.7.20250829.1.nupkg.sha512"}, "Microsoft.WindowsAppSDK/1.8.250907003": {"type": "package", "serviceable": true, "sha512": "sha512-FCTiOXXnp9EGvVAuLtQc9LT41kj4JZ1Nis9pTrNCubjOrIQAzpJdA3OfWuFCMktsx/s/nWbpQ1JQ4jUAQQDoLA==", "path": "microsoft.windowsappsdk/1.8.250907003", "hashPath": "microsoft.windowsappsdk.1.8.250907003.nupkg.sha512"}, "Microsoft.WindowsAppSDK.AI/1.8.37": {"type": "package", "serviceable": true, "sha512": "sha512-WvH7ur+R2N8c3deB8y7q7+Wwx7zybkC6LMS/KNqSYXlSOr75/WCZYwqwrPHJ/63YIUVhka7nJos9g4rIe7SFCw==", "path": "microsoft.windowsappsdk.ai/1.8.37", "hashPath": "microsoft.windowsappsdk.ai.1.8.37.nupkg.sha512"}, "Microsoft.WindowsAppSDK.Base/1.8.250831001": {"type": "package", "serviceable": true, "sha512": "sha512-8LlfXBS2Hpw+OoVXViJmIOPXl0nMbqMaFR3j6+QHFNc62VULwPEcXiMRcP2WbV/+mtC7W2LH6yx6uu/Hrr9lVw==", "path": "microsoft.windowsappsdk.base/1.8.250831001", "hashPath": "microsoft.windowsappsdk.base.1.8.250831001.nupkg.sha512"}, "Microsoft.WindowsAppSDK.DWrite/1.8.25090401": {"type": "package", "serviceable": true, "sha512": "sha512-WJ0p9yMgiNYqU2O5ZKCXcb7FBjryIUUopgeYMvnlf1yBUYgdjMFMkoJqYVqkz866wnntiB2IZhLxEzhFzvVs1A==", "path": "microsoft.windowsappsdk.dwrite/1.8.25090401", "hashPath": "microsoft.windowsappsdk.dwrite.1.8.25090401.nupkg.sha512"}, "Microsoft.WindowsAppSDK.Foundation/1.8.250906002": {"type": "package", "serviceable": true, "sha512": "sha512-ltIXeHUX0AATpqmx/oBcRK+zhtK0KAfoGqItlQRlef9kG7Itj9iXAI+1EdFr4cQYzHzFM3PPLszEWDyR633svA==", "path": "microsoft.windowsappsdk.foundation/1.8.250906002", "hashPath": "microsoft.windowsappsdk.foundation.1.8.250906002.nupkg.sha512"}, "Microsoft.WindowsAppSDK.InteractiveExperiences/1.8.250906004": {"type": "package", "serviceable": true, "sha512": "sha512-UoK2yeZiycD1DmADHZz+hcMAoOaUfXLc9qUPfOjmVeKQ6i5ghGMjx/nd49bksP3wVhmSGHxb3argRKWPkK5maw==", "path": "microsoft.windowsappsdk.interactiveexperiences/1.8.250906004", "hashPath": "microsoft.windowsappsdk.interactiveexperiences.1.8.250906004.nupkg.sha512"}, "Microsoft.WindowsAppSDK.Runtime/1.8.250907003": {"type": "package", "serviceable": true, "sha512": "sha512-URsthdat9pv1wnRNAy0WA5yejsc47QsSjjJ+L6INEgIFilrp4/LYndpHkoWh3KwBSjwkskvZlSprbOl09YVg/g==", "path": "microsoft.windowsappsdk.runtime/1.8.250907003", "hashPath": "microsoft.windowsappsdk.runtime.1.8.250907003.nupkg.sha512"}, "Microsoft.WindowsAppSDK.Widgets/1.8.250904007": {"type": "package", "serviceable": true, "sha512": "sha512-sgwdXYhb8S4JjBmWWiFxALT1xK0fJeAbisolctmodMX7tlvBXDgUyvl/GHfTQ61DGIiW+kokX61WR46L2YlhAA==", "path": "microsoft.windowsappsdk.widgets/1.8.250904007", "hashPath": "microsoft.windowsappsdk.widgets.1.8.250904007.nupkg.sha512"}, "Microsoft.WindowsAppSDK.WinUI/1.8.250906003": {"type": "package", "serviceable": true, "sha512": "sha512-6oskwUluqlDGwUcwYlY3GWTMLajyjh9e790SmWzCCMDRV6sunYbqp7DkiSLzn8nhgSbGvmj6zG92JnkYRlbrXw==", "path": "microsoft.windowsappsdk.winui/1.8.250906003", "hashPath": "microsoft.windowsappsdk.winui.1.8.250906003.nupkg.sha512"}, "Microsoft.Web.WebView2.Core.Projection/1.0.3179.45": {"type": "reference", "serviceable": false, "sha512": ""}}, "runtimes": {"win-x64": ["win", "any", "base"]}}