﻿<!-- Copyright (c) Microsoft Corporation.
     Licensed under the MIT License. -->
<Page
    x:Class="CsUnpackagedAppNotifications.Scenario2_ToastWithTextBox"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:CsUnpackagedAppNotifications"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="auto"/>
        </Grid.RowDefinitions>
        <StackPanel Grid.Row="0" Spacing="10" Margin="10,10,10,10">
            <TextBlock Text="Description:" Style="{StaticResource SampleHeaderTextStyle}"/>

            <TextBlock Style="{StaticResource ScenarioDescriptionTextStyle}" TextWrapping="Wrap">
                This example demonstrates using a raw XML payload to produce a local toast notification with an avatar that can handle activation with a quick reply text box.
            </TextBlock>

            <Button Content="Send Toast" Click="SendToast_Click" />
        </StackPanel>
        
        <TextBlock  Grid.Row="1" Style="{StaticResource ScenarioDescriptionTextStyle}" TextWrapping="Wrap">
            Find more information on sending a toast notification at
            <Hyperlink NavigateUri="https://docs.microsoft.com/windows/apps/design/shell/tiles-and-notifications/adaptive-interactive-toasts?tabs=xml">Toast content | Microsoft Docs</Hyperlink>
        </TextBlock>
    </Grid>
</Page>
