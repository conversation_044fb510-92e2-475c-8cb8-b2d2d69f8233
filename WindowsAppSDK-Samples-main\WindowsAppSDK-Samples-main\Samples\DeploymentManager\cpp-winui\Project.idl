﻿// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.

namespace DeploymentManagerSample
{
    /* The following code is scenario/feature-specific IDL.
    Samples authors should modify these runtime classes as appropriate. */

    [default_interface]
    runtimeclass Scenario1_GetStatus : Microsoft.UI.Xaml.Controls.Page
    {
        Scenario1_GetStatus();        
    }

    [default_interface]
    runtimeclass Scenario2_Initialize : Microsoft.UI.Xaml.Controls.Page
    {
        Scenario2_Initialize();
    }

     /* The following code is template-specific IDL.
     These runtime classes are the same across all C++/WinRT WinUI samples. */

    runtimeclass MainPage : Microsoft.UI.Xaml.Controls.Page
    {
        MainPage();
        static MainPage Current();
        void NotifyUser(String strMessage, Microsoft.UI.Xaml.Controls.InfoBarSeverity severity);
    }

    // To use Scenario in a winrt::Windows::Foundation::Collections::IVector<Scenario>, Sc<PERSON>rio should be a WinRT type
    struct Scenario
    {
        String Title;
        String ClassName;
    };

    [default_interface]
    runtimeclass MainWindow : Microsoft.UI.Xaml.Window
    {
        MainWindow();
    }

    [default_interface]
    runtimeclass SettingsPage : Microsoft.UI.Xaml.Controls.Page
    {
        SettingsPage();
    }
}
