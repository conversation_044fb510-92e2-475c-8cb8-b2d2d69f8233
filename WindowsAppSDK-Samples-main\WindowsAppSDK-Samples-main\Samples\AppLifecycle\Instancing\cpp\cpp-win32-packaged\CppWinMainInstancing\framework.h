// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.

#pragma once

#include "targetver.h"
#define WIN32_LEAN_AND_MEAN 
#include <windows.h>
#include <stdlib.h>
#include <malloc.h>
#include <memory.h>
#include <tchar.h>
#include "strsafe.h"
#include <iostream>
#include <fstream>
#include <sstream>

// $(TargetDir)\Generated Files\winrt
#include <winrt/Windows.ApplicationModel.Activation.h>
#include <winrt/Windows.Foundation.h>
#include <winrt/Windows.Foundation.Collections.h>
#include <winrt/Microsoft.Windows.AppLifecycle.h>
#include <winrt/Windows.Storage.h>
