﻿<!-- Copyright (c) Microsoft Corporation.
     Licensed under the MIT License. -->
<Page
    x:Class="cpp_winui_packaged.S2_CrashRecovery"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:cpp_winui_packaged"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d">

    <Grid>
        <ScrollViewer Background="{ThemeResource ApplicationPageBackgroundThemeBrush}">
            <StackPanel Spacing="10" Margin="10,10,10,10">
                <TextBlock Text="Description:" Style="{StaticResource SampleHeaderTextStyle}"/>
                <TextBlock Style="{StaticResource ScenarioDescriptionTextStyle}" TextWrapping="Wrap">
                    Auto-register for recovery and restart in cases of abnormal reboot (app crash or hang)).
                </TextBlock>

                <StackPanel Spacing="10" Margin="10,10,10,10" VerticalAlignment="Center">
                    <TextBlock>Counter:</TextBlock>
                    <TextBlock x:Name="counterTextBlock" FontWeight="Bold"/>
                    <Button x:Name="crashButton" Content="Crash the application" Click="Crash_Click"/>
                </StackPanel>
            </StackPanel>
        </ScrollViewer>
    </Grid>
</Page>
