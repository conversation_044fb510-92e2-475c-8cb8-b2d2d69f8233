﻿// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.

namespace CppUnpackagedAppNotifications
{
    /* The following code is scenario/feature-specific IDL.
    Samples authors should modify these runtime classes as appropriate. */

    struct Notification
    {
        String  Originator;
        String  Action;
        Boolean HasInput;
        String  Input;
    };

    [default_interface]
    runtimeclass Scenario1_ToastWithAvatar : Microsoft.UI.Xaml.Controls.Page
    {
        Scenario1_ToastWithAvatar();
    }

    [default_interface]
    runtimeclass Scenario2_ToastWithTextBox : Microsoft.UI.Xaml.Controls.Page
    {
        Scenario2_ToastWithTextBox();
    }

     /* The following code is template-specific IDL.
     These runtime classes are the same across all C++/WinRT WinUI samples. */

    runtimeclass MainPage : Microsoft.UI.Xaml.Controls.Page
    {
        MainPage();
        static MainPage Current();
        void NotifyUser(String strMessage, Microsoft.UI.Xaml.Controls.InfoBarSeverity severity);
        void NotificationReceived(Notification notification);
    }

    // To use Sc<PERSON><PERSON> in a winrt::Windows::Foundation::Collections::IVector<Scenario>, <PERSON><PERSON><PERSON> should be a WinRT type
    struct Scenario
    {
        String Title;
        String ClassName;
    };

    [default_interface]
    runtimeclass MainWindow : Microsoft.UI.Xaml.Window
    {
        MainWindow();
    }

    [default_interface]
    runtimeclass SettingsPage : Microsoft.UI.Xaml.Controls.Page
    {
        SettingsPage();
    }
}
