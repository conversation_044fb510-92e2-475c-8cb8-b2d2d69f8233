﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)microsoft.windows.sdk.buildtools.msix\1.7.20250829.1\buildTransitive\Microsoft.Windows.SDK.BuildTools.MSIX.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.windows.sdk.buildtools.msix\1.7.20250829.1\buildTransitive\Microsoft.Windows.SDK.BuildTools.MSIX.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.windows.sdk.buildtools\10.0.26100.4948\buildTransitive\Microsoft.Windows.SDK.BuildTools.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.windows.sdk.buildtools\10.0.26100.4948\buildTransitive\Microsoft.Windows.SDK.BuildTools.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.windowsappsdk.base\1.8.250831001\buildTransitive\Microsoft.WindowsAppSDK.Base.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.windowsappsdk.base\1.8.250831001\buildTransitive\Microsoft.WindowsAppSDK.Base.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.windowsappsdk.interactiveexperiences\1.8.250906004\buildTransitive\Microsoft.WindowsAppSDK.InteractiveExperiences.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.windowsappsdk.interactiveexperiences\1.8.250906004\buildTransitive\Microsoft.WindowsAppSDK.InteractiveExperiences.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.windowsappsdk.foundation\1.8.250906002\buildTransitive\Microsoft.WindowsAppSDK.Foundation.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.windowsappsdk.foundation\1.8.250906002\buildTransitive\Microsoft.WindowsAppSDK.Foundation.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.web.webview2\1.0.3179.45\buildTransitive\Microsoft.Web.WebView2.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.web.webview2\1.0.3179.45\buildTransitive\Microsoft.Web.WebView2.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.windowsappsdk.winui\1.8.250906003\buildTransitive\Microsoft.WindowsAppSDK.WinUI.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.windowsappsdk.winui\1.8.250906003\buildTransitive\Microsoft.WindowsAppSDK.WinUI.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.windowsappsdk.widgets\1.8.250904007\buildTransitive\Microsoft.WindowsAppSDK.Widgets.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.windowsappsdk.widgets\1.8.250904007\buildTransitive\Microsoft.WindowsAppSDK.Widgets.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.windowsappsdk.runtime\1.8.250907003\buildTransitive\Microsoft.WindowsAppSDK.Runtime.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.windowsappsdk.runtime\1.8.250907003\buildTransitive\Microsoft.WindowsAppSDK.Runtime.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.windowsappsdk.dwrite\1.8.25090401\buildTransitive\Microsoft.WindowsAppSDK.DWrite.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.windowsappsdk.dwrite\1.8.25090401\buildTransitive\Microsoft.WindowsAppSDK.DWrite.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.windowsappsdk.ai\1.8.37\buildTransitive\Microsoft.WindowsAppSDK.AI.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.windowsappsdk.ai\1.8.37\buildTransitive\Microsoft.WindowsAppSDK.AI.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.windowsappsdk\1.8.250907003\buildTransitive\Microsoft.WindowsAppSDK.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.windowsappsdk\1.8.250907003\buildTransitive\Microsoft.WindowsAppSDK.targets')" />
  </ImportGroup>
</Project>