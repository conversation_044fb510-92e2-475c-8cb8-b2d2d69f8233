﻿<!-- Copyright (c) Microsoft Corporation.
     Licensed under the MIT License. -->
<Page
    x:Class="CppUnpackagedAppNotifications.MainPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:CppUnpackagedAppNotifications"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d">

    <Grid Background="{ThemeResource ApplicationPageBackgroundThemeBrush}">
        <NavigationView x:Name="NavView"
                         Loaded="NavView_Loaded"
                         ItemInvoked="NavView_ItemInvoked"
                         BackRequested="NavView_BackRequested">
            <NavigationView.MenuItems>
                <NavigationViewItemHeader x:Name="MainPagesHeader" Content="Scenarios"/>
            </NavigationView.MenuItems>

            <Grid RowDefinitions="3*,*,Auto">
                <ScrollViewer>
                    <Frame Padding="50" x:Name="ContentFrame" Navigated="ContentFrame_Navigated"/>
                </ScrollViewer>
                <Grid RowDefinitions="Auto,*" Grid.Row="1" Margin="50,0,50,40" Background="{ThemeResource ApplicationPageBackgroundThemeBrush}">
                    <TextBlock Grid.Row="0" Margin="10,10,10,0" Style="{StaticResource SectionHeaderTextStyle}">
                        Received Notifications
                    </TextBlock>
                    <ListView x:Name="notificationBox"
                        Grid.Row="1"
                        Background="Transparent"
                        ScrollViewer.VerticalScrollBarVisibility="Visible">
                        <ListView.ItemContainerStyle>
                            <Style TargetType="ListViewItem">
                                <Setter Property="Height" Value="24" />
                                <Setter Property="MinHeight" Value="24" />
                            </Style>
                        </ListView.ItemContainerStyle>
                    </ListView>
                </Grid>
                <InfoBar x:Name="infoBar" Grid.Row="2">
                </InfoBar>
            </Grid>
        </NavigationView>
    </Grid>
</Page>
