﻿<!-- Copyright (c) Microsoft Corporation.
 Licensed under the MIT License. -->
<Window x:Class="CsWpfActivation.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="C# WPF Activation" Height="480" Width="480">

    <DockPanel>
        <ToolBarTray DockPanel.Dock="Top">
            <ToolBar>
                <Button 
                    x:Name="ActivationInfoWasButton" Click="ActivationInfoWasButton_Click" 
                    ToolTip="Get activation info WAS" Content="ActivationInfo WAS"/>
                <Button 
                    x:Name="ActivationInfoUwpButton" Click="ActivationInfoUwpButton_Click" 
                    ToolTip="Get activation info UWP" Content="ActivationInfo UWP"/>
            </ToolBar>
        </ToolBarTray>

        <ListView 
            x:Name="StatusListView" Background="Transparent"
            ScrollViewer.VerticalScrollBarVisibility="Visible">
        </ListView>

    </DockPanel>
</Window>
