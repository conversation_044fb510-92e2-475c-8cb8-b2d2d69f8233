﻿<!-- Copyright (c) Microsoft Corporation.
     Licensed under the MIT License. -->
<Page
    x:Class="Input.InputCursor"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:Input"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d">

    <Grid RowDefinitions="50,*" Margin="30,0,30,0">
        <TextBlock Grid.Row="0" Style="{StaticResource ScenarioDescriptionTextStyle}" TextWrapping="Wrap">
                    Demonstrates creation and usage of InputCursor objects. Use the ComboBox to select a predefined cursor shape,
						then hover the mouse cursor over the box below to see the new shape.
        </TextBlock>
        <Grid Grid.Row="1" HorizontalAlignment="Center" VerticalAlignment="Center">
            <ComboBox x:Name="cursors" Header="Cursor Types" SelectionChanged="cursors_SelectionChanged" MinWidth="300" Background="Tan" Foreground="Black" SelectedIndex="0">
                <x:String>Arrow</x:String>
                <x:String>Cross</x:String>
                <x:String>Hand</x:String>
                <x:String>Help</x:String>
                <x:String>IBeam</x:String>
                <x:String>Person</x:String>
                <x:String>Pin</x:String>
                <x:String>SizeAll</x:String>
                <x:String>SizeNortheastSouthwest</x:String>
                <x:String>SizeNorthSouth</x:String>
                <x:String>SizeNorthwestSoutheast</x:String>
                <x:String>SizeWestEast</x:String>
                <x:String>UniversalNo</x:String>
                <x:String>UpArrow</x:String>
                <x:String>Wait</x:String>
            </ComboBox>
            <local:CursorPanel Background="BlanchedAlmond" Width="300" Height="300" x:Name="panel" Margin="0,75,0,0" />
        </Grid>
    </Grid>
</Page>
