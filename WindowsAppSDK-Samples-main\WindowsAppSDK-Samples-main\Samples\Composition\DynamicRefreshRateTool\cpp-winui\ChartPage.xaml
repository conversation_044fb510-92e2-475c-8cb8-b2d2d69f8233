﻿<Page
    x:Class="DynamicRefreshRateTool.ChartPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:DynamicRefreshRateTool"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d"
    VerticalAlignment="Stretch"
    HorizontalAlignment="Stretch">

    <Grid VerticalAlignment="Stretch" HorizontalAlignment="Stretch">
        <Canvas Name="FpsCanvas" Background="{ThemeResource ForegroundBrush}" PointerMoved="FpsCanvas_PointerMoved" PointerExited="FpsCanvas_PointerExited">
            <Polyline Name="FpsChart" Stroke="{ThemeResource ChartMain}" Fill="{ThemeResource ChartFill}" StrokeThickness="2"></Polyline>

            <Polyline Name="DashLine" Stroke="{ThemeResource ChartDash}" StrokeDashArray="4,4" ></Polyline>

            <Ellipse Canvas.Left="-100" Width="6" Height="6" Fill="{ThemeResource ChartMain}" Name="FpsIndicator"></Ellipse>

            <Border Canvas.Left="-100" Background="{ThemeResource BackgroundBrush2}" BorderBrush="{ThemeResource SystemAccentColor}" BorderThickness="1" Padding="4,2" CornerRadius="4" Name="FpsTooltip">
                <TextBlock Name="FpsTooltipText">FPS: 60.0</TextBlock>
            </Border>

        </Canvas>

        <StackPanel HorizontalAlignment="Center" VerticalAlignment="Bottom" Orientation="Horizontal">
            <StackPanel HorizontalAlignment="Center" Orientation="Vertical" Margin="0,0,32,0">
                <TextBlock FontSize="12" TextAlignment="Center">Displayed FPS History (Seconds)</TextBlock>
                <Slider  VerticalAlignment="Center" Height="30" Minimum="4" Maximum="60" Width="200" Value="16" ValueChanged="Slider_ValueChanged" TickFrequency="10" TickPlacement="Outside"></Slider>
            </StackPanel>
            <StackPanel HorizontalAlignment="Center" Orientation="Vertical">
                <TextBlock FontSize="12" TextAlignment="Center">Smoothing (Number of frames to average)</TextBlock>
                <Slider VerticalAlignment="Center" Height="30" Minimum="1" Maximum="120" Width="200" Value="8" ValueChanged="Slider_ValueChanged_1"  TickFrequency="20" TickPlacement="Outside"></Slider>
            </StackPanel>
        </StackPanel>
    </Grid>
</Page>
