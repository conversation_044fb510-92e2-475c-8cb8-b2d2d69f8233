﻿// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.

#pragma once

#include <stdlib.h>
#include <windows.h>
#include <stdlib.h>
#include <malloc.h>
#include <memory.h>
#include <tchar.h>
#include "strsafe.h"
#include <sstream>
#include <fstream>

#include <winrt/Windows.ApplicationModel.Activation.h>
#include <winrt/Windows.Foundation.h>
#include <winrt/Windows.Foundation.Collections.h>
#include <winrt/Microsoft.Windows.AppLifecycle.h>
#include <winrt/Windows.Storage.h>

#include <MddBootstrap.h>
