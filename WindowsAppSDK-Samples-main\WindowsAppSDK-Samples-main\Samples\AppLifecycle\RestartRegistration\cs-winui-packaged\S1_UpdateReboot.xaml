﻿<!-- Copyright (c) Microsoft Corporation.
     Licensed under the MIT License. -->
<Page
    x:Class="cs_winui_packaged.S1_UpdateReboot"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:cs_winui_packaged"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d">

    <Grid RowDefinitions="*,Auto">
        <ScrollViewer Background="{ThemeResource ApplicationPageBackgroundThemeBrush}">
            <StackPanel Spacing="10" Margin="10,10,10,10">
                <TextBlock Text="Description:" Style="{StaticResource SampleHeaderTextStyle}"/>
                <TextBlock Style="{StaticResource ScenarioDescriptionTextStyle}" TextWrapping="Wrap">
                    Register for update and patch reboot types and recover latest user data.
                </TextBlock>

                <StackPanel Orientation="Vertical" VerticalAlignment="Center" Padding="10,10,10,10">
                    <TextBox x:Name="messageInput" Header="Message to recover in case of restart:" PlaceholderText="" TextChanged="message_TextChanged"/>
                    <Button x:Name="registerButton" Content="Register for restart" Click="register_Click" />
                    <Button x:Name="unregisterButton" Content="Unregister from restart" Click="unregister_Click" IsEnabled="False"/>
                </StackPanel>
                
                <StackPanel x:Name="recoveryArea" Orientation="Vertical" VerticalAlignment="Center" Padding="10,10,10,10" Visibility="Collapsed">
                    <TextBlock FontWeight="Bold">
                        Recovered message:
                    </TextBlock>
                    <TextBlock x:Name="recoveredMessage">
                        None
                    </TextBlock>
                </StackPanel>
            </StackPanel>
        </ScrollViewer>
    </Grid>
</Page>
