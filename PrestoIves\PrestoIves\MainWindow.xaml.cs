﻿using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;
using Microsoft.UI.Xaml.Controls.Primitives;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using Windows.ApplicationModel;
using Windows.Storage;
using Windows.Storage.Search;

namespace PrestoIves
{
    /// <summary>
    /// Ventana principal de la aplicación.
    /// </summary>
    public sealed partial class MainWindow : Window
    {
        public ObservableCollection<ImageFileInfo> Images { get; } = new();

        public MainWindow()
        {
            InitializeComponent();
            _ = GetItemsAsync();
        }

        private async Task GetItemsAsync()
        {
            // Carpeta donde se instalaron los Assets de la app
            StorageFolder appInstalledFolder = Package.Current.InstalledLocation;
            StorageFolder picturesFolder = await appInstalledFolder.GetFolderAsync("Assets\\Samples");

            var result = picturesFolder.CreateFileQueryWithOptions(new QueryOptions());
            IReadOnlyList<StorageFile> imageFiles = await result.GetFilesAsync();

            foreach (StorageFile file in imageFiles)
            {
                Images.Add(await LoadImageInfoAsync(file));
            }

            ImageGridView.ItemsSource = Images;
        }

        public static async Task<ImageFileInfo> LoadImageInfoAsync(StorageFile file)
        {
            var properties = await file.Properties.GetImagePropertiesAsync();
            ImageFileInfo info = new(properties,
                                     file,
                                     file.DisplayName,
                                     file.DisplayType);
            return info;
        }

        private void ImageGridView_ContainerContentChanging(ListViewBase sender, ContainerContentChangingEventArgs args)
        {
            if (args.InRecycleQueue)
            {
                var templateRoot = args.ItemContainer.ContentTemplateRoot as Grid;
                var image = templateRoot.FindName("ItemImage") as Image;
                if (image != null)
                {
                    image.Source = null;
                }
            }

            if (args.Phase == 0)
            {
                args.RegisterUpdateCallback(ShowImage);
                args.Handled = true;
            }
        }

        private async void ShowImage(ListViewBase sender, ContainerContentChangingEventArgs args)
        {
            if (args.Phase == 1)
            {
                var templateRoot = args.ItemContainer.ContentTemplateRoot as Grid;
                var image = templateRoot.FindName("ItemImage") as Image;
                var item = args.Item as ImageFileInfo;

                if (image != null && item != null)
                {
                    image.Source = await item.GetImageThumbnailAsync();
                }
            }
        }
    }
}
