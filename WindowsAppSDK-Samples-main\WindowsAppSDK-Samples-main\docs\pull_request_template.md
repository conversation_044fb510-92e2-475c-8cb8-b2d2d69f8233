<!--
Thank you for your pull request!

Please see https://github.com/microsoft/WindowsAppSDK-Samples/blob/main/docs/samples-guidelines.md for guidelines on
how to best contribute to the Windows App SDK Samples repository!

-->

## Description

Please include a summary of the change and/or which issue is fixed. Please also include relevant motivation and context. List any dependencies that are required for this change.

## Target Release

Please specify which release this PR should align with. e.g., 1.0, 1.1, 1.1 Preview 1.

## Checklist

Note that /azp run currently isn't working for this repo.

- [ ] Sam<PERSON> build and run using the Visual Studio versions listed in the [Windows development docs](https://docs.microsoft.com/windows/apps/windows-app-sdk/set-up-your-development-environment?tabs=stable#2-install-visual-studio).
- [ ] Samples build and run on all supported platforms (x64, x86, ARM64) and configurations (Debug, Release).
- [ ] <PERSON><PERSON> set the minimum supported OS version to Windows 10 version 1809.
- [ ] Samples build clean with no warnings or errors.
- [ ] **[For new samples]**: <PERSON><PERSON> have completed the [sample guidelines checklist](https://github.com/microsoft/WindowsAppSDK-Samples/blob/main/docs/samples-guidelines.md#checklist) and follow [standardization/naming guidelines](https://github.com/microsoft/WindowsAppSDK-Samples/blob/main/docs/samples-guidelines.md#standardization-and-naming).
- [ ] Microsoft employees only: you can validate your changes by following the instructions here: https://www.osgwiki.com/wiki/WindowsAppSDK-<PERSON><PERSON>
