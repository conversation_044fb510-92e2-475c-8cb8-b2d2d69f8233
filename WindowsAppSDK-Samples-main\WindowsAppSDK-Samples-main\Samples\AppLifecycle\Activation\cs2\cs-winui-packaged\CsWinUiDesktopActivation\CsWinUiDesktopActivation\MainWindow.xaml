﻿<!-- Copyright (c) Microsoft Corporation.
 Licensed under the MIT License. -->
<Window
    x:Class="CsWinUiDesktopActivation.MainWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d">

    <StackPanel>
        <CommandBar 
            Background="Transparent" IsOpen="False" DefaultLabelPosition="Right" HorizontalAlignment="Left">
            <AppBarButton 
                x:Name="ActivationInfoButton" Icon="Comment" Label="ActivationInfo" 
                Click="ActivationInfoButton_Click"/>
        </CommandBar>
        
        <ListView 
            x:Name="StatusListView" Background="Transparent"
            ScrollViewer.VerticalScrollBarVisibility="Visible">
            <ListView.ItemContainerStyle>
                <Style TargetType="ListViewItem">
                    <Setter Property="Height" Value="24" />
                    <Setter Property="MinHeight" Value="24" />
                </Style>
            </ListView.ItemContainerStyle>
        </ListView>
    </StackPanel>
</Window>
