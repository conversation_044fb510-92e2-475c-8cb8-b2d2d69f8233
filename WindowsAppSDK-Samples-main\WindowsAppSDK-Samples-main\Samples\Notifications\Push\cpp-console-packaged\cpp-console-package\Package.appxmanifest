﻿<?xml version="1.0" encoding="utf-8"?>

<Package
  xmlns="http://schemas.microsoft.com/appx/manifest/foundation/windows10"
  xmlns:uap="http://schemas.microsoft.com/appx/manifest/uap/windows10"
  xmlns:uap5="http://schemas.microsoft.com/appx/manifest/uap/windows10/5"
  xmlns:rescap="http://schemas.microsoft.com/appx/manifest/foundation/windows10/restrictedcapabilities"
  xmlns:com="http://schemas.microsoft.com/appx/manifest/com/windows10"
  IgnorableNamespaces="uap uap5 rescap com">

  <Identity
  Name="PushNotificationsSample"
  Publisher="CN=Microsoft"
  Version="1.0.0.0" />

  <Properties>
  	<DisplayName>Push Notifications Sample</DisplayName>
  	<PublisherDisplayName>Microsoft Corporation</PublisherDisplayName>
  	<Logo>Images\StoreLogo.png</Logo>
  </Properties>

  <Dependencies>
    <TargetDeviceFamily Name="Windows.Universal" MinVersion="10.0.17763.0" MaxVersionTested="10.0.19041.0" />
  </Dependencies>

  <Resources>
    <Resource Language="x-generate"/>
  </Resources>

  <Applications>
    <Application Id="App"
      Executable="$targetnametoken$.exe"
      EntryPoint="$targetentrypoint$">
      <uap:VisualElements
      	DisplayName="Push Notifications Sample"
	Description="Push Notifications Sample App"
        BackgroundColor="transparent"
        Square150x150Logo="Images\Square150x150Logo.png"
        Square44x44Logo="Images\Square44x44Logo.png">
        <uap:DefaultTile Wide310x150Logo="Images\Wide310x150Logo.png" />
	<uap:SplashScreen Image="Images\SplashScreen.png" uap5:Optional="true"/>
      </uap:VisualElements>
      <Extensions>
      	<com:Extension Category="windows.comServer">
		<com:ComServer>
			<com:ExeServer Executable="cpp-console\cpp-console.exe" DisplayName="SampleBackgroundApp" Arguments="----WindowsAppRuntimePushServer:">
				<com:Class Id="ccd2ae3f-764f-4ae3-be45-9804761b28b2" DisplayName="Windows App SDK Push" />
			</com:ExeServer>
		</com:ComServer>
	</com:Extension>
	<uap:Extension Category="windows.protocol" EntryPoint="Windows.FullTrustApplication">
		<uap:Protocol Name="windowsappruntimetestprotocol-packaged">
			<uap:DisplayName>WindowsAppRuntimeTestProtocol</uap:DisplayName>
		</uap:Protocol>
	</uap:Extension>
      </Extensions>
    </Application>
  </Applications>

  <Capabilities>
    <Capability Name="internetClient" />
    <rescap:Capability Name="runFullTrust" />
  </Capabilities>
</Package>
