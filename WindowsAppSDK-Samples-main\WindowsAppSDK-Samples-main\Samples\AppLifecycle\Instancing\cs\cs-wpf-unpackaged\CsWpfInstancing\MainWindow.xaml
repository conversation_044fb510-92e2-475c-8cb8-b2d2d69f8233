﻿<!-- Copyright (c) Microsoft Corporation.
 Licensed under the MIT License. -->
<Window x:Class="CsWpfInstancing.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="CsWpfInstancing" Height="480" Width="480">

    <DockPanel>
        <ToolBarTray DockPanel.Dock="Top">
            <ToolBar>
                <Button 
                    x:Name="ActivationInfoButton" Click="ActivationInfoButton_Click" 
                    ToolTip="Get activation info" Content="ActivationInfo"/>
                <Button 
                    x:Name="RegisterButton" Click="RegisterButton_Click" 
                    ToolTip="Register for rich activation" Content="Register"/>
                <Button 
                    x:Name="UnregisterButton" Click="UnregisterButton_Click" 
                    ToolTip="Unregister for file activation" Content="Unregister"/>
            </ToolBar>
        </ToolBarTray>

        <ListView 
            x:Name="StatusListView" Background="Transparent"
            ScrollViewer.VerticalScrollBarVisibility="Visible">
        </ListView>
    </DockPanel>
</Window>
