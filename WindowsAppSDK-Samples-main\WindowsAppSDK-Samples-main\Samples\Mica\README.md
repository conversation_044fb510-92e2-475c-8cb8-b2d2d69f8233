---
page_type: sample
languages:
- cpp
- cppwinrt
- xaml
products:
- windows
- windows-app-sdk
name: "Mica material sample"
urlFragment: Mica
description: "Shows how to use the Mica material in different apps with WindowsAppSDK"
extendedZipContent:
- path: LICENSE
  target: LICENSE
---
# Mica application sample

These samples demonstrate how to use the Mica material in different apps and frameworks with WindowsAppSDK.

## Prerequisites

* See [System requirements for Windows app development](https://docs.microsoft.com/windows/apps/windows-app-sdk/system-requirements).
* Make sure that your development environment is set up correctly&mdash;see [Install tools for developing apps for Windows 10 and Windows 11](https://docs.microsoft.com/windows/apps/windows-app-sdk/set-up-your-development-environment).
    * For the unpackaged sample apps, make sure to also install the prerequisites for [deploying unpackaged apps](https://docs.microsoft.com/windows/apps/windows-app-sdk/deploy-unpackaged-apps).

## Building and running any of the samples

* Open the solution file (`.sln`) from the subfolder of your preferred sample in Visual Studio.
* From Visual Studio, either **Start Without Debugging** (Ctrl+F5) or **Start Debugging** (F5).

## Related Links

- [Windows App SDK](https://docs.microsoft.com/windows/apps/windows-app-sdk/)
- [Learn more about Mica](https://docs.microsoft.com/windows/apps/design/style/mica#app-layering-with-mica)
