# Instructions
This sample makes use of the Insights header to define data collection events that can be used to track different events in the application.

## Collecting data locally
The data can be locally saved by using tools.

1. Use [tracelog](https://docs.microsoft.com/windows-hardware/drivers/devtest/tracelog) to start capture.

2. Run your code

3. Stop tracing

## Viewing the data
The above steps will produce an ETL file that contains the data. The data can be viewed using [Windows Performance Analyzer](https://docs.microsoft.com/windows-hardware/drivers/devtest/capture-and-view-tracelogging-data#view-tracelogging-data-using-wpa)
