﻿<!-- Copyright (c) Microsoft Corporation.
     Licensed under the MIT License. -->
<Page
    x:Class="DeploymentManagerSample.Scenario2_Initialize"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:DeploymentManagerSample"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d">

    <Grid>
        <ScrollViewer Background="{ThemeResource ApplicationPageBackgroundThemeBrush}">
            <StackPanel Spacing="10" Margin="10,10,10,10">
                <TextBlock Text="Description:" Style="{StaticResource SampleHeaderTextStyle}"/>
                <TextBlock Style="{StaticResource ScenarioDescriptionTextStyle}" TextWrapping="Wrap">
                    Initialize the Windows App SDK Runtime with DeploymentManager.Initialize()
                </TextBlock>
                <Button Content="Initialize Windows App Runtime" Click="InitializeScenarioButton_Click" />
                <Button Content="Initialize Windows App Runtime with force deploy overload" Click="ForceInitializeScenarioButton_Click" />
                <TextBlock x:Name="resultStatus"></TextBlock>
                <TextBlock x:Name="resultExtendedError" TextWrapping="Wrap"></TextBlock>
                <TextBlock x:Name="resultImplication"></TextBlock>
            </StackPanel>
        </ScrollViewer>
    </Grid>
</Page>
