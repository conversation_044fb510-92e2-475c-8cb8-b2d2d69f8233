﻿<!-- Copyright (c) Microsoft Corporation.
     Licensed under the MIT License. -->
<Page
    x:Class="cs_winui_packaged.S3_RebootTypeDetection"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:cs_winui_packaged"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d">

    <Grid>
        <ScrollViewer Background="{ThemeResource ApplicationPageBackgroundThemeBrush}">
            <StackPanel Spacing="10" Margin="10,10,10,10">
                <TextBlock Text="Description:" Style="{StaticResource ScenarioHeaderTextStyle}"/>
                <TextBlock Style="{StaticResource ScenarioDescriptionTextStyle}" TextWrapping="Wrap">
                    Auto-registers for recovery and restart and shows previous reboot type message if the app was automatically restarted.
                </TextBlock>

                <StackPanel Spacing="10" Margin="10,10,10,10" VerticalAlignment="Center">
                    <Button x:Name="crashButton" Content="Crash the application" Click="crash_Click"/>
                    <Button x:Name="restartButton" Content="Simulate regular application restart" Click="restart_Click"/>
                    <TextBlock x:Name="restartTypeTextBlock">App was not automatically restarted from this scenario.</TextBlock>
                </StackPanel>
            </StackPanel>
        </ScrollViewer>
    </Grid>
</Page>
