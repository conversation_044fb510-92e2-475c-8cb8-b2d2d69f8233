﻿<?xml version="1.0" encoding="utf-8"?>
<Page
    x:Class="WinFormsWithIsland.WinUI3Page"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:WinFormsWithIsland"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d"
    >
    <StackPanel Margin="10">
        <Button x:Name="MyButton" Click="Button_Click">I am a WinUI 3 Button</Button>
        <TextBox Text="I am a WinUI 3 TextBox" />
        <ComboBox ItemsSource="{x:Bind MyItems}" />
    </StackPanel>
</Page>
