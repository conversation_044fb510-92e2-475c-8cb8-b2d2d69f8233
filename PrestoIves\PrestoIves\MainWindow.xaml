<?xml version="1.0" encoding="utf-8"?>
<Window
    x:Class="PrestoIves.MainWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:PrestoIves"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d"
    Title="PrestoIves">

    <Window.SystemBackdrop>
        <MicaBackdrop />
    </Window.SystemBackdrop>

    <Grid>
        <Grid.Resources>

            <Style x:Key="ImageGridView_ItemContainerStyle"
            TargetType="GridViewItem">
                <Setter Property="Background" Value="Gray"/>
                <Setter Property="Margin" Value="8"/>
            </Style>

            <DataTemplate x:Key="ImageGridView_ItemTemplate"
                          x:DataType="local:ImageFileInfo">
                <Grid Height="300"
                  Width="300"
                  Margin="8">
                    <Grid.RowDefinitions>
                        <RowDefinition />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>

                    <Image x:Name="ItemImage"
               Source="Assets/StoreLogo.png"
               Stretch="Uniform" />

                    <StackPanel Orientation="Vertical"
                    Grid.Row="1">
                        <TextBlock Text="{x:Bind ImageTitle}"
                       HorizontalAlignment="Center"
                       Style="{StaticResource SubtitleTextBlockStyle}" />
                        <StackPanel Orientation="Horizontal"
                        HorizontalAlignment="Center">
                            <TextBlock Text="{x:Bind ImageFileType}"
                           HorizontalAlignment="Center"
                           Style="{StaticResource CaptionTextBlockStyle}" />
                            <TextBlock Text="{x:Bind ImageDimensions}"
                           HorizontalAlignment="Center"
                           Style="{StaticResource CaptionTextBlockStyle}"
                           Margin="8,0,0,0" />
                        </StackPanel>

                        <RatingControl Value="{x:Bind ImageRating}"/>
                    </StackPanel>
                </Grid>
            </DataTemplate>
            <ItemsPanelTemplate x:Key="ImageGridView_ItemsPanelTemplate">
                <ItemsWrapGrid Orientation="Horizontal"
                           HorizontalAlignment="Center"/>
            </ItemsPanelTemplate>

        </Grid.Resources>
        <GridView x:Name="ImageGridView"
                ItemTemplate="{StaticResource ImageGridView_ItemTemplate}"
                ItemContainerStyle="{StaticResource ImageGridView_ItemContainerStyle}"
                Background ="Bisque"
                HorizontalAlignment="Center"
                ItemsPanel="{StaticResource ImageGridView_ItemsPanelTemplate}"
                ContainerContentChanging="ImageGridView_ContainerContentChanging"/>
        






    </Grid>
</Window>
