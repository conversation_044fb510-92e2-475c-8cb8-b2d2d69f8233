﻿<Project Sdk="Microsoft.NET.Sdk">
	<PropertyGroup>
		<TargetFramework>net6.0-windows10.0.19041.0</TargetFramework>
		<TargetPlatformMinVersion>10.0.17763.0</TargetPlatformMinVersion>
		<RootNamespace>WinUIComponentCs</RootNamespace>
		<RuntimeIdentifiers>win10-x86;win10-x64;win10-arm64</RuntimeIdentifiers>
		<UseWinUI>true</UseWinUI>
		<CsWinRTComponent>true</CsWinRTComponent>
		<Platforms>x64;x86;ARM64</Platforms>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.Windows.CsWinRT" Version="2.0.0" />
		<PackageReference Include="Microsoft.WindowsAppSDK" Version="1.5.240227000" />
		<PackageReference Include="Microsoft.Windows.SDK.BuildTools" Version="10.0.22621.3233" />
	</ItemGroup>

	<ItemGroup>
	  <Page Update="NameReporter.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </Page>
	</ItemGroup>

</Project>
