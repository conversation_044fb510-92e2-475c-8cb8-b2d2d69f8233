﻿<!-- Copyright (c) Microsoft Corporation.
     Licensed under the MIT License. -->
<Page
    x:Class="CsApp.Scenario1_CustomControl"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:CsApp"
    xmlns:custom="using:WinUIComponentCs"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d">

    <Grid RowDefinitions="*,Auto">
        <ScrollViewer Background="{ThemeResource ApplicationPageBackgroundThemeBrush}">
            <StackPanel Spacing="10" Margin="10,10,10,10">

                <TextBlock Text="Description:" Style="{StaticResource SampleHeaderTextStyle}"/>
                <TextBlock Style="{StaticResource ScenarioDescriptionTextStyle}" TextWrapping="Wrap">
                    Reference a Custom WinUI Button type from a C# component.
                </TextBlock>

                <!-- Reference CustomButton type -->
                <custom:CustomButton x:Name="MyCustomButton" Content="Hover"/>

            </StackPanel>
        </ScrollViewer>
    </Grid>
</Page>
