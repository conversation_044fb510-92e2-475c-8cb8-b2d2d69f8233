---
page_type: sample
languages:
- csharp
products:
- windows
- windows-app-sdk
name: "Input Samples"
urlFragment: Input
description: "Showcases Microsoft.UI.Input API usage."
extendedZipContent:
- path: LICENSE
  target: LICENSE
---

# Input samples

These samples demonstrate how to use the WinAppSDK input APIs within the namespace Microsoft.UI.Input. This sample also demonstrates the working of various Keyboard events and Keyboard accelerators using WinUI. 

## Prerequisites

* See [System requirements for Windows app development](https://docs.microsoft.com/windows/apps/windows-app-sdk/system-requirements).
* Make sure that your development environment is set up correctly&mdash;see [Install tools for developing apps for Windows 10 and Windows 11](https://docs.microsoft.com/windows/apps/windows-app-sdk/set-up-your-development-environment).

## Building and running any of the samples

* Open the solution file (`.sln`) from the subfolder of your preferred sample in Visual Studio.
* From Visual Studio, either **Start Without Debugging** (Ctrl+F5) or **Start Debugging** (F5).

## Related Links

- [Windows App SDK](https://docs.microsoft.com/windows/apps/windows-app-sdk/)
