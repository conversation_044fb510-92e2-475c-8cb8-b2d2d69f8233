{"version": 3, "targets": {"net8.0-windows10.0.19041": {"Microsoft.Web.WebView2/1.0.3179.45": {"type": "package", "build": {"buildTransitive/Microsoft.Web.WebView2.targets": {}}, "runtimeTargets": {"runtimes/win-arm64/native/WebView2Loader.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/WebView2Loader.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/WebView2Loader.dll": {"assetType": "native", "rid": "win-x86"}}}, "Microsoft.Windows.SDK.BuildTools/10.0.26100.4948": {"type": "package", "build": {"buildTransitive/Microsoft.Windows.SDK.BuildTools.props": {}, "buildTransitive/Microsoft.Windows.SDK.BuildTools.targets": {}}}, "Microsoft.Windows.SDK.BuildTools.MSIX/1.7.20250829.1": {"type": "package", "build": {"buildTransitive/Microsoft.Windows.SDK.BuildTools.MSIX.props": {}, "buildTransitive/Microsoft.Windows.SDK.BuildTools.MSIX.targets": {}}}, "Microsoft.WindowsAppSDK/1.8.250907003": {"type": "package", "dependencies": {"Microsoft.WindowsAppSDK.AI": "[1.8.37]", "Microsoft.WindowsAppSDK.Base": "[1.8.250831001]", "Microsoft.WindowsAppSDK.DWrite": "[1.8.25090401]", "Microsoft.WindowsAppSDK.Foundation": "[1.8.250906002]", "Microsoft.WindowsAppSDK.InteractiveExperiences": "[1.8.250906004]", "Microsoft.WindowsAppSDK.Runtime": "[1.8.250907003]", "Microsoft.WindowsAppSDK.Widgets": "[1.8.250904007]", "Microsoft.WindowsAppSDK.WinUI": "[1.8.250906003]"}, "build": {"buildTransitive/Microsoft.WindowsAppSDK.props": {}, "buildTransitive/Microsoft.WindowsAppSDK.targets": {}}}, "Microsoft.WindowsAppSDK.AI/1.8.37": {"type": "package", "dependencies": {"Microsoft.WindowsAppSDK.Base": "1.8.250831001", "Microsoft.WindowsAppSDK.Foundation": "1.8.250906002"}, "compile": {"lib/net6.0-windows10.0.17763.0/Microsoft.Graphics.Imaging.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.ContentSafety.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.Foundation.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.Imaging.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.Text.Projection.dll": {}}, "runtime": {"lib/net6.0-windows10.0.17763.0/Microsoft.Graphics.Imaging.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.ContentSafety.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.Foundation.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.Imaging.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.Text.Projection.dll": {}}, "build": {"buildTransitive/Microsoft.WindowsAppSDK.AI.props": {}, "buildTransitive/Microsoft.WindowsAppSDK.AI.targets": {}}}, "Microsoft.WindowsAppSDK.Base/1.8.250831001": {"type": "package", "dependencies": {"Microsoft.Windows.SDK.BuildTools": "10.0.26100.4654", "Microsoft.Windows.SDK.BuildTools.MSIX": "1.7.20250829.1"}, "build": {"buildTransitive/Microsoft.WindowsAppSDK.Base.props": {}, "buildTransitive/Microsoft.WindowsAppSDK.Base.targets": {}}}, "Microsoft.WindowsAppSDK.DWrite/1.8.25090401": {"type": "package", "dependencies": {"Microsoft.WindowsAppSDK.Base": "1.8.250831001"}, "build": {"buildTransitive/Microsoft.WindowsAppSDK.DWrite.props": {}, "buildTransitive/Microsoft.WindowsAppSDK.DWrite.targets": {}}}, "Microsoft.WindowsAppSDK.Foundation/1.8.250906002": {"type": "package", "dependencies": {"Microsoft.WindowsAppSDK.Base": "1.8.250831001", "Microsoft.WindowsAppSDK.InteractiveExperiences": "1.8.250906004"}, "compile": {"lib/net6.0-windows10.0.17763.0/Microsoft.Security.Authentication.OAuth.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppLifecycle.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppNotifications.Builder.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppNotifications.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.Background.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.Resources.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.BadgeNotifications.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Foundation.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Management.Deployment.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Media.Capture.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.PushNotifications.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Security.AccessControl.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Storage.Pickers.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Storage.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.System.Power.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.System.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.WindowsAppRuntime.Bootstrap.Net.dll": {}}, "runtime": {"lib/net6.0-windows10.0.17763.0/Microsoft.Security.Authentication.OAuth.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppLifecycle.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppNotifications.Builder.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppNotifications.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.Background.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.Resources.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.BadgeNotifications.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Foundation.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Management.Deployment.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Media.Capture.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.PushNotifications.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Security.AccessControl.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Storage.Pickers.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Storage.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.System.Power.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.System.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.WindowsAppRuntime.Bootstrap.Net.dll": {}}, "build": {"buildTransitive/Microsoft.WindowsAppSDK.Foundation.props": {}, "buildTransitive/Microsoft.WindowsAppSDK.Foundation.targets": {}}, "runtimeTargets": {"runtimes/win-arm64/native/Microsoft.Windows.ApplicationModel.Background.UniversalBGTask.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-arm64/native/Microsoft.WindowsAppRuntime.Bootstrap.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-arm64ec/native/Microsoft.Windows.ApplicationModel.Background.UniversalBGTask.dll": {"assetType": "native", "rid": "win-arm64ec"}, "runtimes/win-arm64ec/native/Microsoft.WindowsAppRuntime.Bootstrap.dll": {"assetType": "native", "rid": "win-arm64ec"}, "runtimes/win-x64/native/Microsoft.Windows.ApplicationModel.Background.UniversalBGTask.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/Microsoft.WindowsAppRuntime.Bootstrap.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/Microsoft.Windows.ApplicationModel.Background.UniversalBGTask.dll": {"assetType": "native", "rid": "win-x86"}, "runtimes/win-x86/native/Microsoft.WindowsAppRuntime.Bootstrap.dll": {"assetType": "native", "rid": "win-x86"}}}, "Microsoft.WindowsAppSDK.InteractiveExperiences/1.8.250906004": {"type": "package", "dependencies": {"Microsoft.WindowsAppSDK.Base": "1.8.250831001"}, "compile": {"lib/net6.0-windows10.0.18362.0/Microsoft.InteractiveExperiences.Projection.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0-windows10.0.18362.0/Microsoft.InteractiveExperiences.Projection.dll": {"related": ".xml"}}, "build": {"buildTransitive/Microsoft.WindowsAppSDK.InteractiveExperiences.props": {}, "buildTransitive/Microsoft.WindowsAppSDK.InteractiveExperiences.targets": {}}}, "Microsoft.WindowsAppSDK.Runtime/1.8.250907003": {"type": "package", "dependencies": {"Microsoft.WindowsAppSDK.Base": "1.8.250831001"}, "build": {"buildTransitive/Microsoft.WindowsAppSDK.Runtime.props": {}, "buildTransitive/Microsoft.WindowsAppSDK.Runtime.targets": {}}}, "Microsoft.WindowsAppSDK.Widgets/1.8.250904007": {"type": "package", "dependencies": {"Microsoft.WindowsAppSDK.Base": "1.8.250831001"}, "compile": {"lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Widgets.Projection.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Widgets.Projection.dll": {"related": ".xml"}}, "build": {"buildTransitive/Microsoft.WindowsAppSDK.Widgets.props": {}, "buildTransitive/Microsoft.WindowsAppSDK.Widgets.targets": {}}}, "Microsoft.WindowsAppSDK.WinUI/1.8.250906003": {"type": "package", "dependencies": {"Microsoft.Web.WebView2": "1.0.3179.45", "Microsoft.WindowsAppSDK.Base": "1.8.250831001", "Microsoft.WindowsAppSDK.Foundation": "1.8.250906002", "Microsoft.WindowsAppSDK.InteractiveExperiences": "1.8.250906004"}, "compile": {"lib/net6.0-windows10.0.17763.0/Microsoft.WinUI.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0-windows10.0.17763.0/Microsoft.WinUI.dll": {"related": ".xml"}}, "build": {"buildTransitive/Microsoft.WindowsAppSDK.WinUI.props": {}, "buildTransitive/Microsoft.WindowsAppSDK.WinUI.targets": {}}}}, "net8.0-windows10.0.19041/win-arm64": {"Microsoft.Web.WebView2/1.0.3179.45": {"type": "package", "native": {"runtimes/win-arm64/native/WebView2Loader.dll": {}}, "build": {"buildTransitive/Microsoft.Web.WebView2.targets": {}}}, "Microsoft.Windows.SDK.BuildTools/10.0.26100.4948": {"type": "package", "build": {"buildTransitive/Microsoft.Windows.SDK.BuildTools.props": {}, "buildTransitive/Microsoft.Windows.SDK.BuildTools.targets": {}}}, "Microsoft.Windows.SDK.BuildTools.MSIX/1.7.20250829.1": {"type": "package", "build": {"buildTransitive/Microsoft.Windows.SDK.BuildTools.MSIX.props": {}, "buildTransitive/Microsoft.Windows.SDK.BuildTools.MSIX.targets": {}}}, "Microsoft.WindowsAppSDK/1.8.250907003": {"type": "package", "dependencies": {"Microsoft.WindowsAppSDK.AI": "[1.8.37]", "Microsoft.WindowsAppSDK.Base": "[1.8.250831001]", "Microsoft.WindowsAppSDK.DWrite": "[1.8.25090401]", "Microsoft.WindowsAppSDK.Foundation": "[1.8.250906002]", "Microsoft.WindowsAppSDK.InteractiveExperiences": "[1.8.250906004]", "Microsoft.WindowsAppSDK.Runtime": "[1.8.250907003]", "Microsoft.WindowsAppSDK.Widgets": "[1.8.250904007]", "Microsoft.WindowsAppSDK.WinUI": "[1.8.250906003]"}, "build": {"buildTransitive/Microsoft.WindowsAppSDK.props": {}, "buildTransitive/Microsoft.WindowsAppSDK.targets": {}}}, "Microsoft.WindowsAppSDK.AI/1.8.37": {"type": "package", "dependencies": {"Microsoft.WindowsAppSDK.Base": "1.8.250831001", "Microsoft.WindowsAppSDK.Foundation": "1.8.250906002"}, "compile": {"lib/net6.0-windows10.0.17763.0/Microsoft.Graphics.Imaging.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.ContentSafety.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.Foundation.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.Imaging.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.Text.Projection.dll": {}}, "runtime": {"lib/net6.0-windows10.0.17763.0/Microsoft.Graphics.Imaging.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.ContentSafety.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.Foundation.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.Imaging.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.Text.Projection.dll": {}}, "build": {"buildTransitive/Microsoft.WindowsAppSDK.AI.props": {}, "buildTransitive/Microsoft.WindowsAppSDK.AI.targets": {}}}, "Microsoft.WindowsAppSDK.Base/1.8.250831001": {"type": "package", "dependencies": {"Microsoft.Windows.SDK.BuildTools": "10.0.26100.4654", "Microsoft.Windows.SDK.BuildTools.MSIX": "1.7.20250829.1"}, "build": {"buildTransitive/Microsoft.WindowsAppSDK.Base.props": {}, "buildTransitive/Microsoft.WindowsAppSDK.Base.targets": {}}}, "Microsoft.WindowsAppSDK.DWrite/1.8.25090401": {"type": "package", "dependencies": {"Microsoft.WindowsAppSDK.Base": "1.8.250831001"}, "build": {"buildTransitive/Microsoft.WindowsAppSDK.DWrite.props": {}, "buildTransitive/Microsoft.WindowsAppSDK.DWrite.targets": {}}}, "Microsoft.WindowsAppSDK.Foundation/1.8.250906002": {"type": "package", "dependencies": {"Microsoft.WindowsAppSDK.Base": "1.8.250831001", "Microsoft.WindowsAppSDK.InteractiveExperiences": "1.8.250906004"}, "compile": {"lib/net6.0-windows10.0.17763.0/Microsoft.Security.Authentication.OAuth.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppLifecycle.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppNotifications.Builder.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppNotifications.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.Background.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.Resources.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.BadgeNotifications.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Foundation.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Management.Deployment.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Media.Capture.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.PushNotifications.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Security.AccessControl.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Storage.Pickers.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Storage.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.System.Power.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.System.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.WindowsAppRuntime.Bootstrap.Net.dll": {}}, "runtime": {"lib/net6.0-windows10.0.17763.0/Microsoft.Security.Authentication.OAuth.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppLifecycle.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppNotifications.Builder.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppNotifications.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.Background.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.Resources.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.BadgeNotifications.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Foundation.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Management.Deployment.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Media.Capture.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.PushNotifications.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Security.AccessControl.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Storage.Pickers.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Storage.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.System.Power.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.System.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.WindowsAppRuntime.Bootstrap.Net.dll": {}}, "native": {"runtimes/win-arm64/native/Microsoft.Windows.ApplicationModel.Background.UniversalBGTask.dll": {}, "runtimes/win-arm64/native/Microsoft.WindowsAppRuntime.Bootstrap.dll": {}}, "build": {"buildTransitive/Microsoft.WindowsAppSDK.Foundation.props": {}, "buildTransitive/Microsoft.WindowsAppSDK.Foundation.targets": {}}}, "Microsoft.WindowsAppSDK.InteractiveExperiences/1.8.250906004": {"type": "package", "dependencies": {"Microsoft.WindowsAppSDK.Base": "1.8.250831001"}, "compile": {"lib/net6.0-windows10.0.18362.0/Microsoft.InteractiveExperiences.Projection.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0-windows10.0.18362.0/Microsoft.InteractiveExperiences.Projection.dll": {"related": ".xml"}}, "build": {"buildTransitive/Microsoft.WindowsAppSDK.InteractiveExperiences.props": {}, "buildTransitive/Microsoft.WindowsAppSDK.InteractiveExperiences.targets": {}}}, "Microsoft.WindowsAppSDK.Runtime/1.8.250907003": {"type": "package", "dependencies": {"Microsoft.WindowsAppSDK.Base": "1.8.250831001"}, "build": {"buildTransitive/Microsoft.WindowsAppSDK.Runtime.props": {}, "buildTransitive/Microsoft.WindowsAppSDK.Runtime.targets": {}}}, "Microsoft.WindowsAppSDK.Widgets/1.8.250904007": {"type": "package", "dependencies": {"Microsoft.WindowsAppSDK.Base": "1.8.250831001"}, "compile": {"lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Widgets.Projection.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Widgets.Projection.dll": {"related": ".xml"}}, "build": {"buildTransitive/Microsoft.WindowsAppSDK.Widgets.props": {}, "buildTransitive/Microsoft.WindowsAppSDK.Widgets.targets": {}}}, "Microsoft.WindowsAppSDK.WinUI/1.8.250906003": {"type": "package", "dependencies": {"Microsoft.Web.WebView2": "1.0.3179.45", "Microsoft.WindowsAppSDK.Base": "1.8.250831001", "Microsoft.WindowsAppSDK.Foundation": "1.8.250906002", "Microsoft.WindowsAppSDK.InteractiveExperiences": "1.8.250906004"}, "compile": {"lib/net6.0-windows10.0.17763.0/Microsoft.WinUI.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0-windows10.0.17763.0/Microsoft.WinUI.dll": {"related": ".xml"}}, "build": {"buildTransitive/Microsoft.WindowsAppSDK.WinUI.props": {}, "buildTransitive/Microsoft.WindowsAppSDK.WinUI.targets": {}}}}, "net8.0-windows10.0.19041/win-x64": {"Microsoft.Web.WebView2/1.0.3179.45": {"type": "package", "native": {"runtimes/win-x64/native/WebView2Loader.dll": {}}, "build": {"buildTransitive/Microsoft.Web.WebView2.targets": {}}}, "Microsoft.Windows.SDK.BuildTools/10.0.26100.4948": {"type": "package", "build": {"buildTransitive/Microsoft.Windows.SDK.BuildTools.props": {}, "buildTransitive/Microsoft.Windows.SDK.BuildTools.targets": {}}}, "Microsoft.Windows.SDK.BuildTools.MSIX/1.7.20250829.1": {"type": "package", "build": {"buildTransitive/Microsoft.Windows.SDK.BuildTools.MSIX.props": {}, "buildTransitive/Microsoft.Windows.SDK.BuildTools.MSIX.targets": {}}}, "Microsoft.WindowsAppSDK/1.8.250907003": {"type": "package", "dependencies": {"Microsoft.WindowsAppSDK.AI": "[1.8.37]", "Microsoft.WindowsAppSDK.Base": "[1.8.250831001]", "Microsoft.WindowsAppSDK.DWrite": "[1.8.25090401]", "Microsoft.WindowsAppSDK.Foundation": "[1.8.250906002]", "Microsoft.WindowsAppSDK.InteractiveExperiences": "[1.8.250906004]", "Microsoft.WindowsAppSDK.Runtime": "[1.8.250907003]", "Microsoft.WindowsAppSDK.Widgets": "[1.8.250904007]", "Microsoft.WindowsAppSDK.WinUI": "[1.8.250906003]"}, "build": {"buildTransitive/Microsoft.WindowsAppSDK.props": {}, "buildTransitive/Microsoft.WindowsAppSDK.targets": {}}}, "Microsoft.WindowsAppSDK.AI/1.8.37": {"type": "package", "dependencies": {"Microsoft.WindowsAppSDK.Base": "1.8.250831001", "Microsoft.WindowsAppSDK.Foundation": "1.8.250906002"}, "compile": {"lib/net6.0-windows10.0.17763.0/Microsoft.Graphics.Imaging.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.ContentSafety.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.Foundation.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.Imaging.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.Text.Projection.dll": {}}, "runtime": {"lib/net6.0-windows10.0.17763.0/Microsoft.Graphics.Imaging.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.ContentSafety.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.Foundation.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.Imaging.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.Text.Projection.dll": {}}, "build": {"buildTransitive/Microsoft.WindowsAppSDK.AI.props": {}, "buildTransitive/Microsoft.WindowsAppSDK.AI.targets": {}}}, "Microsoft.WindowsAppSDK.Base/1.8.250831001": {"type": "package", "dependencies": {"Microsoft.Windows.SDK.BuildTools": "10.0.26100.4654", "Microsoft.Windows.SDK.BuildTools.MSIX": "1.7.20250829.1"}, "build": {"buildTransitive/Microsoft.WindowsAppSDK.Base.props": {}, "buildTransitive/Microsoft.WindowsAppSDK.Base.targets": {}}}, "Microsoft.WindowsAppSDK.DWrite/1.8.25090401": {"type": "package", "dependencies": {"Microsoft.WindowsAppSDK.Base": "1.8.250831001"}, "build": {"buildTransitive/Microsoft.WindowsAppSDK.DWrite.props": {}, "buildTransitive/Microsoft.WindowsAppSDK.DWrite.targets": {}}}, "Microsoft.WindowsAppSDK.Foundation/1.8.250906002": {"type": "package", "dependencies": {"Microsoft.WindowsAppSDK.Base": "1.8.250831001", "Microsoft.WindowsAppSDK.InteractiveExperiences": "1.8.250906004"}, "compile": {"lib/net6.0-windows10.0.17763.0/Microsoft.Security.Authentication.OAuth.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppLifecycle.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppNotifications.Builder.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppNotifications.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.Background.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.Resources.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.BadgeNotifications.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Foundation.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Management.Deployment.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Media.Capture.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.PushNotifications.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Security.AccessControl.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Storage.Pickers.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Storage.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.System.Power.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.System.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.WindowsAppRuntime.Bootstrap.Net.dll": {}}, "runtime": {"lib/net6.0-windows10.0.17763.0/Microsoft.Security.Authentication.OAuth.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppLifecycle.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppNotifications.Builder.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppNotifications.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.Background.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.Resources.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.BadgeNotifications.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Foundation.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Management.Deployment.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Media.Capture.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.PushNotifications.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Security.AccessControl.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Storage.Pickers.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Storage.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.System.Power.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.System.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.WindowsAppRuntime.Bootstrap.Net.dll": {}}, "native": {"runtimes/win-x64/native/Microsoft.Windows.ApplicationModel.Background.UniversalBGTask.dll": {}, "runtimes/win-x64/native/Microsoft.WindowsAppRuntime.Bootstrap.dll": {}}, "build": {"buildTransitive/Microsoft.WindowsAppSDK.Foundation.props": {}, "buildTransitive/Microsoft.WindowsAppSDK.Foundation.targets": {}}}, "Microsoft.WindowsAppSDK.InteractiveExperiences/1.8.250906004": {"type": "package", "dependencies": {"Microsoft.WindowsAppSDK.Base": "1.8.250831001"}, "compile": {"lib/net6.0-windows10.0.18362.0/Microsoft.InteractiveExperiences.Projection.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0-windows10.0.18362.0/Microsoft.InteractiveExperiences.Projection.dll": {"related": ".xml"}}, "build": {"buildTransitive/Microsoft.WindowsAppSDK.InteractiveExperiences.props": {}, "buildTransitive/Microsoft.WindowsAppSDK.InteractiveExperiences.targets": {}}}, "Microsoft.WindowsAppSDK.Runtime/1.8.250907003": {"type": "package", "dependencies": {"Microsoft.WindowsAppSDK.Base": "1.8.250831001"}, "build": {"buildTransitive/Microsoft.WindowsAppSDK.Runtime.props": {}, "buildTransitive/Microsoft.WindowsAppSDK.Runtime.targets": {}}}, "Microsoft.WindowsAppSDK.Widgets/1.8.250904007": {"type": "package", "dependencies": {"Microsoft.WindowsAppSDK.Base": "1.8.250831001"}, "compile": {"lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Widgets.Projection.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Widgets.Projection.dll": {"related": ".xml"}}, "build": {"buildTransitive/Microsoft.WindowsAppSDK.Widgets.props": {}, "buildTransitive/Microsoft.WindowsAppSDK.Widgets.targets": {}}}, "Microsoft.WindowsAppSDK.WinUI/1.8.250906003": {"type": "package", "dependencies": {"Microsoft.Web.WebView2": "1.0.3179.45", "Microsoft.WindowsAppSDK.Base": "1.8.250831001", "Microsoft.WindowsAppSDK.Foundation": "1.8.250906002", "Microsoft.WindowsAppSDK.InteractiveExperiences": "1.8.250906004"}, "compile": {"lib/net6.0-windows10.0.17763.0/Microsoft.WinUI.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0-windows10.0.17763.0/Microsoft.WinUI.dll": {"related": ".xml"}}, "build": {"buildTransitive/Microsoft.WindowsAppSDK.WinUI.props": {}, "buildTransitive/Microsoft.WindowsAppSDK.WinUI.targets": {}}}}, "net8.0-windows10.0.19041/win-x86": {"Microsoft.Web.WebView2/1.0.3179.45": {"type": "package", "native": {"runtimes/win-x86/native/WebView2Loader.dll": {}}, "build": {"buildTransitive/Microsoft.Web.WebView2.targets": {}}}, "Microsoft.Windows.SDK.BuildTools/10.0.26100.4948": {"type": "package", "build": {"buildTransitive/Microsoft.Windows.SDK.BuildTools.props": {}, "buildTransitive/Microsoft.Windows.SDK.BuildTools.targets": {}}}, "Microsoft.Windows.SDK.BuildTools.MSIX/1.7.20250829.1": {"type": "package", "build": {"buildTransitive/Microsoft.Windows.SDK.BuildTools.MSIX.props": {}, "buildTransitive/Microsoft.Windows.SDK.BuildTools.MSIX.targets": {}}}, "Microsoft.WindowsAppSDK/1.8.250907003": {"type": "package", "dependencies": {"Microsoft.WindowsAppSDK.AI": "[1.8.37]", "Microsoft.WindowsAppSDK.Base": "[1.8.250831001]", "Microsoft.WindowsAppSDK.DWrite": "[1.8.25090401]", "Microsoft.WindowsAppSDK.Foundation": "[1.8.250906002]", "Microsoft.WindowsAppSDK.InteractiveExperiences": "[1.8.250906004]", "Microsoft.WindowsAppSDK.Runtime": "[1.8.250907003]", "Microsoft.WindowsAppSDK.Widgets": "[1.8.250904007]", "Microsoft.WindowsAppSDK.WinUI": "[1.8.250906003]"}, "build": {"buildTransitive/Microsoft.WindowsAppSDK.props": {}, "buildTransitive/Microsoft.WindowsAppSDK.targets": {}}}, "Microsoft.WindowsAppSDK.AI/1.8.37": {"type": "package", "dependencies": {"Microsoft.WindowsAppSDK.Base": "1.8.250831001", "Microsoft.WindowsAppSDK.Foundation": "1.8.250906002"}, "compile": {"lib/net6.0-windows10.0.17763.0/Microsoft.Graphics.Imaging.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.ContentSafety.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.Foundation.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.Imaging.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.Text.Projection.dll": {}}, "runtime": {"lib/net6.0-windows10.0.17763.0/Microsoft.Graphics.Imaging.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.ContentSafety.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.Foundation.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.Imaging.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.Text.Projection.dll": {}}, "build": {"buildTransitive/Microsoft.WindowsAppSDK.AI.props": {}, "buildTransitive/Microsoft.WindowsAppSDK.AI.targets": {}}}, "Microsoft.WindowsAppSDK.Base/1.8.250831001": {"type": "package", "dependencies": {"Microsoft.Windows.SDK.BuildTools": "10.0.26100.4654", "Microsoft.Windows.SDK.BuildTools.MSIX": "1.7.20250829.1"}, "build": {"buildTransitive/Microsoft.WindowsAppSDK.Base.props": {}, "buildTransitive/Microsoft.WindowsAppSDK.Base.targets": {}}}, "Microsoft.WindowsAppSDK.DWrite/1.8.25090401": {"type": "package", "dependencies": {"Microsoft.WindowsAppSDK.Base": "1.8.250831001"}, "build": {"buildTransitive/Microsoft.WindowsAppSDK.DWrite.props": {}, "buildTransitive/Microsoft.WindowsAppSDK.DWrite.targets": {}}}, "Microsoft.WindowsAppSDK.Foundation/1.8.250906002": {"type": "package", "dependencies": {"Microsoft.WindowsAppSDK.Base": "1.8.250831001", "Microsoft.WindowsAppSDK.InteractiveExperiences": "1.8.250906004"}, "compile": {"lib/net6.0-windows10.0.17763.0/Microsoft.Security.Authentication.OAuth.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppLifecycle.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppNotifications.Builder.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppNotifications.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.Background.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.Resources.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.BadgeNotifications.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Foundation.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Management.Deployment.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Media.Capture.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.PushNotifications.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Security.AccessControl.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Storage.Pickers.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Storage.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.System.Power.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.System.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.WindowsAppRuntime.Bootstrap.Net.dll": {}}, "runtime": {"lib/net6.0-windows10.0.17763.0/Microsoft.Security.Authentication.OAuth.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppLifecycle.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppNotifications.Builder.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppNotifications.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.Background.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.Resources.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.BadgeNotifications.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Foundation.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Management.Deployment.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Media.Capture.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.PushNotifications.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Security.AccessControl.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Storage.Pickers.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Storage.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.System.Power.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.System.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.WindowsAppRuntime.Bootstrap.Net.dll": {}}, "native": {"runtimes/win-x86/native/Microsoft.Windows.ApplicationModel.Background.UniversalBGTask.dll": {}, "runtimes/win-x86/native/Microsoft.WindowsAppRuntime.Bootstrap.dll": {}}, "build": {"buildTransitive/Microsoft.WindowsAppSDK.Foundation.props": {}, "buildTransitive/Microsoft.WindowsAppSDK.Foundation.targets": {}}}, "Microsoft.WindowsAppSDK.InteractiveExperiences/1.8.250906004": {"type": "package", "dependencies": {"Microsoft.WindowsAppSDK.Base": "1.8.250831001"}, "compile": {"lib/net6.0-windows10.0.18362.0/Microsoft.InteractiveExperiences.Projection.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0-windows10.0.18362.0/Microsoft.InteractiveExperiences.Projection.dll": {"related": ".xml"}}, "build": {"buildTransitive/Microsoft.WindowsAppSDK.InteractiveExperiences.props": {}, "buildTransitive/Microsoft.WindowsAppSDK.InteractiveExperiences.targets": {}}}, "Microsoft.WindowsAppSDK.Runtime/1.8.250907003": {"type": "package", "dependencies": {"Microsoft.WindowsAppSDK.Base": "1.8.250831001"}, "build": {"buildTransitive/Microsoft.WindowsAppSDK.Runtime.props": {}, "buildTransitive/Microsoft.WindowsAppSDK.Runtime.targets": {}}}, "Microsoft.WindowsAppSDK.Widgets/1.8.250904007": {"type": "package", "dependencies": {"Microsoft.WindowsAppSDK.Base": "1.8.250831001"}, "compile": {"lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Widgets.Projection.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Widgets.Projection.dll": {"related": ".xml"}}, "build": {"buildTransitive/Microsoft.WindowsAppSDK.Widgets.props": {}, "buildTransitive/Microsoft.WindowsAppSDK.Widgets.targets": {}}}, "Microsoft.WindowsAppSDK.WinUI/1.8.250906003": {"type": "package", "dependencies": {"Microsoft.Web.WebView2": "1.0.3179.45", "Microsoft.WindowsAppSDK.Base": "1.8.250831001", "Microsoft.WindowsAppSDK.Foundation": "1.8.250906002", "Microsoft.WindowsAppSDK.InteractiveExperiences": "1.8.250906004"}, "compile": {"lib/net6.0-windows10.0.17763.0/Microsoft.WinUI.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0-windows10.0.17763.0/Microsoft.WinUI.dll": {"related": ".xml"}}, "build": {"buildTransitive/Microsoft.WindowsAppSDK.WinUI.props": {}, "buildTransitive/Microsoft.WindowsAppSDK.WinUI.targets": {}}}}}, "libraries": {"Microsoft.Web.WebView2/1.0.3179.45": {"sha512": "3pokSH5CnN0G6rGhGFo1y87inxYhNxBQ2Vdf0wlvBj99KHxQJormjDACmqRnFeUsmuNFIhWwfAL1ztq7wD5qRA==", "type": "package", "path": "microsoft.web.webview2/1.0.3179.45", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "NOTICE.txt", "WebView2.idl", "WebView2.tlb", "build/Common.targets", "build/Microsoft.Web.WebView2.targets", "build/WebView2Rules.Project.xml", "build/native/Microsoft.Web.WebView2.targets", "build/native/arm64/WebView2Loader.dll", "build/native/arm64/WebView2Loader.dll.lib", "build/native/arm64/WebView2LoaderStatic.lib", "build/native/include-winrt/WebView2Interop.h", "build/native/include-winrt/WebView2Interop.idl", "build/native/include-winrt/WebView2Interop.tlb", "build/native/include/WebView2.h", "build/native/include/WebView2EnvironmentOptions.h", "build/native/x64/WebView2Loader.dll", "build/native/x64/WebView2Loader.dll.lib", "build/native/x64/WebView2LoaderStatic.lib", "build/native/x86/WebView2Loader.dll", "build/native/x86/WebView2Loader.dll.lib", "build/native/x86/WebView2LoaderStatic.lib", "build/wv2winrt.targets", "buildTransitive/Microsoft.Web.WebView2.targets", "lib/Microsoft.Web.WebView2.Core.winmd", "lib/net462/Microsoft.Web.WebView2.Core.dll", "lib/net462/Microsoft.Web.WebView2.Core.xml", "lib/net462/Microsoft.Web.WebView2.WinForms.dll", "lib/net462/Microsoft.Web.WebView2.WinForms.xml", "lib/net462/Microsoft.Web.WebView2.Wpf.dll", "lib/net462/Microsoft.Web.WebView2.Wpf.xml", "lib_manual/net5.0-windows10.0.17763.0/Microsoft.Web.WebView2.Wpf.dll", "lib_manual/net5.0-windows10.0.17763.0/Microsoft.Web.WebView2.Wpf.xml", "lib_manual/net6.0-windows10.0.17763.0/Microsoft.Web.WebView2.Core.Projection.dll", "lib_manual/net8.0-windows10.0.17763.0/Microsoft.Web.WebView2.Core.Projection.dll", "lib_manual/netcoreapp3.0/Microsoft.Web.WebView2.Core.dll", "lib_manual/netcoreapp3.0/Microsoft.Web.WebView2.Core.xml", "lib_manual/netcoreapp3.0/Microsoft.Web.WebView2.WinForms.dll", "lib_manual/netcoreapp3.0/Microsoft.Web.WebView2.WinForms.xml", "lib_manual/netcoreapp3.0/Microsoft.Web.WebView2.Wpf.dll", "lib_manual/netcoreapp3.0/Microsoft.Web.WebView2.Wpf.xml", "microsoft.web.webview2.1.0.3179.45.nupkg.sha512", "microsoft.web.webview2.nuspec", "runtimes/win-arm64/native/WebView2Loader.dll", "runtimes/win-arm64/native_uap/Microsoft.Web.WebView2.Core.dll", "runtimes/win-x64/native/WebView2Loader.dll", "runtimes/win-x64/native_uap/Microsoft.Web.WebView2.Core.dll", "runtimes/win-x86/native/WebView2Loader.dll", "runtimes/win-x86/native_uap/Microsoft.Web.WebView2.Core.dll", "tools/VisualStudioToolsManifest.xml", "tools/wv2winrt/Antlr3.Runtime.dll", "tools/wv2winrt/Antlr4.StringTemplate.dll", "tools/wv2winrt/System.Buffers.dll", "tools/wv2winrt/System.CommandLine.DragonFruit.dll", "tools/wv2winrt/System.CommandLine.Rendering.dll", "tools/wv2winrt/System.CommandLine.dll", "tools/wv2winrt/System.Memory.dll", "tools/wv2winrt/System.Numerics.Vectors.dll", "tools/wv2winrt/System.Runtime.CompilerServices.Unsafe.dll", "tools/wv2winrt/codegen_util.dll", "tools/wv2winrt/concrt140_app.dll", "tools/wv2winrt/cs/System.CommandLine.resources.dll", "tools/wv2winrt/de/System.CommandLine.resources.dll", "tools/wv2winrt/es/System.CommandLine.resources.dll", "tools/wv2winrt/fr/System.CommandLine.resources.dll", "tools/wv2winrt/it/System.CommandLine.resources.dll", "tools/wv2winrt/ja/System.CommandLine.resources.dll", "tools/wv2winrt/ko/System.CommandLine.resources.dll", "tools/wv2winrt/msvcp140_1_app.dll", "tools/wv2winrt/msvcp140_2_app.dll", "tools/wv2winrt/msvcp140_app.dll", "tools/wv2winrt/pl/System.CommandLine.resources.dll", "tools/wv2winrt/pt-BR/System.CommandLine.resources.dll", "tools/wv2winrt/ru/System.CommandLine.resources.dll", "tools/wv2winrt/tr/System.CommandLine.resources.dll", "tools/wv2winrt/type_hierarchy.dll", "tools/wv2winrt/vcamp140_app.dll", "tools/wv2winrt/vccorlib140_app.dll", "tools/wv2winrt/vcomp140_app.dll", "tools/wv2winrt/vcruntime140_app.dll", "tools/wv2winrt/winrt_winmd.dll", "tools/wv2winrt/winrt_winmd.winmd", "tools/wv2winrt/wv2winrt.exe", "tools/wv2winrt/wv2winrt.exe.config", "tools/wv2winrt/wv2winrt.xml", "tools/wv2winrt/zh-Hans/System.CommandLine.resources.dll", "tools/wv2winrt/zh-Hant/System.CommandLine.resources.dll"]}, "Microsoft.Windows.SDK.BuildTools/10.0.26100.4948": {"sha512": "o0T4CVaumDjPNNijKiM7p25vHKdyKqYvaVVLgQO02KTOoUDlgMYJVUQAXn1IG0G9/ZsdZ+bdgWxgQsrO/b37qw==", "type": "package", "path": "microsoft.windows.sdk.buildtools/10.0.26100.4948", "files": [".nupkg.metadata", ".signature.p7s", "bin/10.0.26100.0/arm64/AccChecker/Microsoft.Diagnostics.Tracing.EventSource.dll", "bin/10.0.26100.0/arm64/ComparePackage.exe", "bin/10.0.26100.0/arm64/DeployUtil.exe", "bin/10.0.26100.0/arm64/MakeCert.exe", "bin/10.0.26100.0/arm64/Microsoft.ComparePackage.Lib.dll", "bin/10.0.26100.0/arm64/Microsoft.Diagnostics.Tracing.EventSource.dll", "bin/10.0.26100.0/arm64/Microsoft.PackageEditor.Lib.dll", "bin/10.0.26100.0/arm64/Microsoft.Tools.Connectivity.dll", "bin/10.0.26100.0/arm64/Microsoft.Tools.Deploy.dll", "bin/10.0.26100.0/arm64/Microsoft.Windows.Build.Appx.AppxPackaging.dll.manifest", "bin/10.0.26100.0/arm64/Microsoft.Windows.Build.Appx.AppxSip.dll.manifest", "bin/10.0.26100.0/arm64/Microsoft.Windows.Build.Appx.OpcServices.dll.manifest", "bin/10.0.26100.0/arm64/Microsoft.Windows.Build.Signing.mssign32.dll.manifest", "bin/10.0.26100.0/arm64/Microsoft.Windows.Build.Signing.wintrust.dll.manifest", "bin/10.0.26100.0/arm64/PackageEditor.exe", "bin/10.0.26100.0/arm64/ServicingCommon.dll", "bin/10.0.26100.0/arm64/SirepClient.assembly.manifest", "bin/10.0.26100.0/arm64/SirepClient.dll", "bin/10.0.26100.0/arm64/SirepInterop.dll", "bin/10.0.26100.0/arm64/SshClient.dll", "bin/10.0.26100.0/arm64/WinAppDeployCmd.exe", "bin/10.0.26100.0/arm64/WinAppDeployCommon.dll", "bin/10.0.26100.0/arm64/appxpackaging.dll", "bin/10.0.26100.0/arm64/appxsip.dll", "bin/10.0.26100.0/arm64/en-US/AppxPackaging.dll.mui", "bin/10.0.26100.0/arm64/en/Microsoft.Tools.Deploy.resources.dll", "bin/10.0.26100.0/arm64/en/WinAppDeployCmd.resources.dll", "bin/10.0.26100.0/arm64/ipoverusb.discoverpartners.dll", "bin/10.0.26100.0/arm64/makeappx.exe", "bin/10.0.26100.0/arm64/makecat.exe", "bin/10.0.26100.0/arm64/makecat.exe.manifest", "bin/10.0.26100.0/arm64/makepri.exe", "bin/10.0.26100.0/arm64/mc.exe", "bin/10.0.26100.0/arm64/mdmerge.exe", "bin/10.0.26100.0/arm64/midl.exe", "bin/10.0.26100.0/arm64/midlc.exe", "bin/10.0.26100.0/arm64/midlrt.exe", "bin/10.0.26100.0/arm64/midlrtmd.dll", "bin/10.0.26100.0/arm64/mrmsupport.dll", "bin/10.0.26100.0/arm64/msisip.dll", "bin/10.0.26100.0/arm64/mssign32.dll", "bin/10.0.26100.0/arm64/mt.exe", "bin/10.0.26100.0/arm64/mt.exe.config", "bin/10.0.26100.0/arm64/opcservices.dll", "bin/10.0.26100.0/arm64/rc.exe", "bin/10.0.26100.0/arm64/rcdll.dll", "bin/10.0.26100.0/arm64/signtool.exe", "bin/10.0.26100.0/arm64/signtool.exe.manifest", "bin/10.0.26100.0/arm64/tracewpp.exe", "bin/10.0.26100.0/arm64/uuidgen.exe", "bin/10.0.26100.0/arm64/veiid.exe", "bin/10.0.26100.0/arm64/winmdidl.exe", "bin/10.0.26100.0/arm64/wintrust.dll", "bin/10.0.26100.0/arm64/wintrust.dll.ini", "bin/10.0.26100.0/x64/AccChecker/Microsoft.Diagnostics.Tracing.EventSource.dll", "bin/10.0.26100.0/x64/ComparePackage.exe", "bin/10.0.26100.0/x64/DeployUtil.exe", "bin/10.0.26100.0/x64/MakeCert.exe", "bin/10.0.26100.0/x64/Microsoft.ComparePackage.Lib.dll", "bin/10.0.26100.0/x64/Microsoft.Diagnostics.Tracing.EventSource.dll", "bin/10.0.26100.0/x64/Microsoft.PackageEditor.Lib.dll", "bin/10.0.26100.0/x64/Microsoft.Tools.Connectivity.dll", "bin/10.0.26100.0/x64/Microsoft.Tools.Deploy.dll", "bin/10.0.26100.0/x64/Microsoft.Windows.Build.Appx.AppxPackaging.dll.manifest", "bin/10.0.26100.0/x64/Microsoft.Windows.Build.Appx.AppxSip.dll.manifest", "bin/10.0.26100.0/x64/Microsoft.Windows.Build.Appx.OpcServices.dll.manifest", "bin/10.0.26100.0/x64/Microsoft.Windows.Build.Signing.mssign32.dll.manifest", "bin/10.0.26100.0/x64/Microsoft.Windows.Build.Signing.wintrust.dll.manifest", "bin/10.0.26100.0/x64/PackageEditor.exe", "bin/10.0.26100.0/x64/ServicingCommon.dll", "bin/10.0.26100.0/x64/SirepClient.assembly.manifest", "bin/10.0.26100.0/x64/SirepClient.dll", "bin/10.0.26100.0/x64/SirepInterop.dll", "bin/10.0.26100.0/x64/SshClient.dll", "bin/10.0.26100.0/x64/WinAppDeployCmd.exe", "bin/10.0.26100.0/x64/WinAppDeployCommon.dll", "bin/10.0.26100.0/x64/appxpackaging.dll", "bin/10.0.26100.0/x64/appxsip.dll", "bin/10.0.26100.0/x64/en-US/AppxPackaging.dll.mui", "bin/10.0.26100.0/x64/en/Microsoft.Tools.Deploy.resources.dll", "bin/10.0.26100.0/x64/en/WinAppDeployCmd.resources.dll", "bin/10.0.26100.0/x64/ipoverusb.discoverpartners.dll", "bin/10.0.26100.0/x64/makeappx.exe", "bin/10.0.26100.0/x64/makecat.exe", "bin/10.0.26100.0/x64/makecat.exe.manifest", "bin/10.0.26100.0/x64/makepri.exe", "bin/10.0.26100.0/x64/mc.exe", "bin/10.0.26100.0/x64/mdmerge.exe", "bin/10.0.26100.0/x64/midl.exe", "bin/10.0.26100.0/x64/midlc.exe", "bin/10.0.26100.0/x64/midlrt.exe", "bin/10.0.26100.0/x64/midlrtmd.dll", "bin/10.0.26100.0/x64/mrmsupport.dll", "bin/10.0.26100.0/x64/msisip.dll", "bin/10.0.26100.0/x64/mssign32.dll", "bin/10.0.26100.0/x64/mt.exe", "bin/10.0.26100.0/x64/mt.exe.config", "bin/10.0.26100.0/x64/opcservices.dll", "bin/10.0.26100.0/x64/rc.exe", "bin/10.0.26100.0/x64/rcdll.dll", "bin/10.0.26100.0/x64/signtool.exe", "bin/10.0.26100.0/x64/signtool.exe.manifest", "bin/10.0.26100.0/x64/tracewpp.exe", "bin/10.0.26100.0/x64/uuidgen.exe", "bin/10.0.26100.0/x64/veiid.exe", "bin/10.0.26100.0/x64/winmdidl.exe", "bin/10.0.26100.0/x64/wintrust.dll", "bin/10.0.26100.0/x64/wintrust.dll.ini", "bin/10.0.26100.0/x86/AccChecker/Microsoft.Diagnostics.Tracing.EventSource.dll", "bin/10.0.26100.0/x86/ComparePackage.exe", "bin/10.0.26100.0/x86/DeployUtil.exe", "bin/10.0.26100.0/x86/MakeCert.exe", "bin/10.0.26100.0/x86/Microsoft.ComparePackage.Lib.dll", "bin/10.0.26100.0/x86/Microsoft.Diagnostics.Tracing.EventSource.dll", "bin/10.0.26100.0/x86/Microsoft.PackageEditor.Lib.dll", "bin/10.0.26100.0/x86/Microsoft.Tools.Connectivity.dll", "bin/10.0.26100.0/x86/Microsoft.Tools.Deploy.dll", "bin/10.0.26100.0/x86/Microsoft.Windows.Build.Appx.AppxPackaging.dll.manifest", "bin/10.0.26100.0/x86/Microsoft.Windows.Build.Appx.AppxSip.dll.manifest", "bin/10.0.26100.0/x86/Microsoft.Windows.Build.Appx.OpcServices.dll.manifest", "bin/10.0.26100.0/x86/Microsoft.Windows.Build.Signing.mssign32.dll.manifest", "bin/10.0.26100.0/x86/Microsoft.Windows.Build.Signing.wintrust.dll.manifest", "bin/10.0.26100.0/x86/PackageEditor.exe", "bin/10.0.26100.0/x86/ServicingCommon.dll", "bin/10.0.26100.0/x86/SirepClient.assembly.manifest", "bin/10.0.26100.0/x86/SirepClient.dll", "bin/10.0.26100.0/x86/SirepInterop.dll", "bin/10.0.26100.0/x86/SshClient.dll", "bin/10.0.26100.0/x86/WinAppDeployCmd.exe", "bin/10.0.26100.0/x86/WinAppDeployCommon.dll", "bin/10.0.26100.0/x86/appxpackaging.dll", "bin/10.0.26100.0/x86/appxsip.dll", "bin/10.0.26100.0/x86/en-US/AppxPackaging.dll.mui", "bin/10.0.26100.0/x86/en/Microsoft.Tools.Deploy.resources.dll", "bin/10.0.26100.0/x86/en/WinAppDeployCmd.resources.dll", "bin/10.0.26100.0/x86/ipoverusb.discoverpartners.dll", "bin/10.0.26100.0/x86/makeappx.exe", "bin/10.0.26100.0/x86/makecat.exe", "bin/10.0.26100.0/x86/makecat.exe.manifest", "bin/10.0.26100.0/x86/makepri.exe", "bin/10.0.26100.0/x86/mc.exe", "bin/10.0.26100.0/x86/mdmerge.exe", "bin/10.0.26100.0/x86/midl.exe", "bin/10.0.26100.0/x86/midlc.exe", "bin/10.0.26100.0/x86/midlrt.exe", "bin/10.0.26100.0/x86/midlrtmd.dll", "bin/10.0.26100.0/x86/mrmsupport.dll", "bin/10.0.26100.0/x86/msisip.dll", "bin/10.0.26100.0/x86/mssign32.dll", "bin/10.0.26100.0/x86/mt.exe", "bin/10.0.26100.0/x86/mt.exe.config", "bin/10.0.26100.0/x86/opcservices.dll", "bin/10.0.26100.0/x86/rc.exe", "bin/10.0.26100.0/x86/rcdll.dll", "bin/10.0.26100.0/x86/signtool.exe", "bin/10.0.26100.0/x86/signtool.exe.manifest", "bin/10.0.26100.0/x86/tracewpp.exe", "bin/10.0.26100.0/x86/uuidgen.exe", "bin/10.0.26100.0/x86/winmdidl.exe", "bin/10.0.26100.0/x86/wintrust.dll", "bin/10.0.26100.0/x86/wintrust.dll.ini", "build/Microsoft.Windows.SDK.BuildTools.props", "build/Microsoft.Windows.SDK.BuildTools.targets", "buildTransitive/Microsoft.Windows.SDK.BuildTools.props", "buildTransitive/Microsoft.Windows.SDK.BuildTools.targets", "microsoft.windows.sdk.buildtools.10.0.26100.4948.nupkg.sha512", "microsoft.windows.sdk.buildtools.nuspec", "schemas/10.0.26100.0/winrt/AppxManifestSchema.xsd", "schemas/10.0.26100.0/winrt/AppxManifestSchema2010_v2.xsd", "schemas/10.0.26100.0/winrt/AppxManifestSchema2013.xsd", "schemas/10.0.26100.0/winrt/FoundationManifestSchema.xsd", "schemas/10.0.26100.0/winrt/FoundationManifestSchema_v2.xsd", "schemas/10.0.26100.0/winrt/UapManifestSchema.xsd", "schemas/10.0.26100.0/winrt/UapManifestSchema_v10.xsd", "schemas/10.0.26100.0/winrt/UapManifestSchema_v11.xsd", "schemas/10.0.26100.0/winrt/UapManifestSchema_v12.xsd", "schemas/10.0.26100.0/winrt/UapManifestSchema_v13.xsd", "schemas/10.0.26100.0/winrt/UapManifestSchema_v15.xsd", "schemas/10.0.26100.0/winrt/UapManifestSchema_v15a.xsd", "schemas/10.0.26100.0/winrt/UapManifestSchema_v16.xsd", "schemas/10.0.26100.0/winrt/UapManifestSchema_v17.xsd", "schemas/10.0.26100.0/winrt/UapManifestSchema_v18.xsd", "schemas/10.0.26100.0/winrt/UapManifestSchema_v2.xsd", "schemas/10.0.26100.0/winrt/UapManifestSchema_v3.xsd", "schemas/10.0.26100.0/winrt/UapManifestSchema_v4.xsd", "schemas/10.0.26100.0/winrt/UapManifestSchema_v5.xsd", "schemas/10.0.26100.0/winrt/UapManifestSchema_v6.xsd", "schemas/10.0.26100.0/winrt/UapManifestSchema_v7.xsd", "schemas/10.0.26100.0/winrt/UapManifestSchema_v8.xsd"]}, "Microsoft.Windows.SDK.BuildTools.MSIX/1.7.20250829.1": {"sha512": "IMdvRmCIZnBS5GkYnv0po1bcx6U1OF39pqA4TphQ9evDzpCRoSE19/PkDvlUNNrBavTsLIEJgd/TAIFner75ow==", "type": "package", "path": "microsoft.windows.sdk.buildtools.msix/1.7.20250829.1", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "CodeSignSummary-537b207f-b567-40b5-8f47-b95c567fae6c.md", "CodeSignSummary-b140a053-633e-42ca-8749-f25e57218abb.md", "NOTICE.txt", "build/AppDevPackageScripts/Add-AppDevPackage.ps1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/cs-CZ/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/de-DE/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/en-US/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/es-ES/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/fr-FR/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/it-IT/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/ja-JP/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/ko-KR/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/pl-PL/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/pt-BR/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/ru-RU/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/tr-TR/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/zh-CN/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/zh-TW/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Install.ps1", "build/AppDevPackageScripts/LogSideloadingTelemetry.ps1", "build/Landing/extras/br.png", "build/Landing/extras/br_snippet.png", "build/Landing/image.png", "build/Landing/index.template.html", "build/Landing/logo.png", "build/Landing/style.css", "build/Microsoft.Windows.SDK.BuildTools.MSIX.BeforeCommon.props", "build/Microsoft.Windows.SDK.BuildTools.MSIX.Common.props", "build/Microsoft.Windows.SDK.BuildTools.MSIX.Common.targets", "build/Microsoft.Windows.SDK.BuildTools.MSIX.Cpp.props", "build/Microsoft.Windows.SDK.BuildTools.MSIX.Cpp.targets", "build/Microsoft.Windows.SDK.BuildTools.MSIX.Cs.targets", "build/Microsoft.Windows.SDK.BuildTools.MSIX.DesignTime.targets", "build/Microsoft.Windows.SDK.BuildTools.MSIX.MrtCore.PriExpansion.targets", "build/Microsoft.Windows.SDK.BuildTools.MSIX.MrtCore.PriGen.targets", "build/Microsoft.Windows.SDK.BuildTools.MSIX.MrtCore.Tasks.targets", "build/Microsoft.Windows.SDK.BuildTools.MSIX.MrtCore.targets", "build/Microsoft.Windows.SDK.BuildTools.MSIX.Packaging.targets", "build/Microsoft.Windows.SDK.BuildTools.MSIX.Pri.targets", "build/Microsoft.Windows.SDK.BuildTools.MSIX.props", "build/Microsoft.Windows.SDK.BuildTools.MSIX.targets", "build/ProjectItemsSchema.xaml", "build/Rules/MsixPackageDebugPropertyPage.xaml", "build/Rules/WindowsPackageTypePropertyPage.xaml", "build/Templates/Package.appinstaller", "buildTransitive/Microsoft.Windows.SDK.BuildTools.MSIX.props", "buildTransitive/Microsoft.Windows.SDK.BuildTools.MSIX.targets", "microsoft.windows.sdk.buildtools.msix.1.7.20250829.1.nupkg.sha512", "microsoft.windows.sdk.buildtools.msix.nuspec", "nuget_icon.png", "sdk_license.txt", "tools/net472/Microsoft.Cci.dll", "tools/net472/Microsoft.VisualStudio.RemoteControl.dll", "tools/net472/Microsoft.VisualStudio.Telemetry.dll", "tools/net472/Microsoft.VisualStudio.Utilities.Internal.dll", "tools/net472/Microsoft.Windows.SDK.BuildTools.MSIX.dll", "tools/net472/Newtonsoft.Json.dll", "tools/net6.0/Microsoft.Cci.dll", "tools/net6.0/Microsoft.VisualStudio.RemoteControl.dll", "tools/net6.0/Microsoft.VisualStudio.Telemetry.dll", "tools/net6.0/Microsoft.VisualStudio.Utilities.Internal.dll", "tools/net6.0/Microsoft.Windows.SDK.BuildTools.MSIX.dll", "tools/net6.0/Newtonsoft.Json.dll"]}, "Microsoft.WindowsAppSDK/1.8.250907003": {"sha512": "FCTiOXXnp9EGvVAuLtQc9LT41kj4JZ1Nis9pTrNCubjOrIQAzpJdA3OfWuFCMktsx/s/nWbpQ1JQ4jUAQQDoLA==", "type": "package", "path": "microsoft.windowsappsdk/1.8.250907003", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "NOTICE.txt", "build/Microsoft.WindowsAppSDK.props", "build/Microsoft.WindowsAppSDK.targets", "build/native/Microsoft.WindowsAppSDK.props", "build/native/Microsoft.WindowsAppSDK.targets", "buildTransitive/Microsoft.WindowsAppSDK.props", "buildTransitive/Microsoft.WindowsAppSDK.targets", "buildTransitive/native/Microsoft.WindowsAppSDK.props", "buildTransitive/native/Microsoft.WindowsAppSDK.targets", "license.txt", "microsoft.windowsappsdk.1.8.250907003.nupkg.sha512", "microsoft.windowsappsdk.nuspec"]}, "Microsoft.WindowsAppSDK.AI/1.8.37": {"sha512": "WvH7ur+R2N8c3deB8y7q7+Wwx7zybkC6LMS/KNqSYXlSOr75/WCZYwqwrPHJ/63YIUVhka7nJos9g4rIe7SFCw==", "type": "package", "path": "microsoft.windowsappsdk.ai/1.8.37", "files": [".nupkg.metadata", ".signature.p7s", "build/Microsoft.WindowsAppSDK.AI.props", "build/Microsoft.WindowsAppSDK.AI.targets", "build/native/Microsoft.WindowsAppSDK.AI.props", "build/native/Microsoft.WindowsAppSDK.AI.targets", "buildTransitive/Microsoft.WindowsAppSDK.AI.props", "buildTransitive/Microsoft.WindowsAppSDK.AI.targets", "buildTransitive/native/Microsoft.WindowsAppSDK.AI.props", "buildTransitive/native/Microsoft.WindowsAppSDK.AI.targets", "lib/net6.0-windows10.0.17763.0/Microsoft.Graphics.Imaging.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.ContentSafety.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.Foundation.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.Imaging.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.Text.Projection.dll", "license.txt", "metadata/Microsoft.Graphics.Imaging.winmd", "metadata/Microsoft.Windows.AI.ContentSafety.winmd", "metadata/Microsoft.Windows.AI.Foundation.winmd", "metadata/Microsoft.Windows.AI.Imaging.winmd", "metadata/Microsoft.Windows.AI.Text.winmd", "metadata/Microsoft.Windows.AI.winmd", "metadata/Microsoft.Windows.Workloads.winmd", "microsoft.windowsappsdk.ai.1.8.37.nupkg.sha512", "microsoft.windowsappsdk.ai.nuspec", "runtimes-framework/package.appxfragment", "runtimes-framework/win-arm64/native/Microsoft.Graphics.Imaging.dll", "runtimes-framework/win-arm64/native/Microsoft.Graphics.ImagingInternal.ImageObjectRemover.winmd", "runtimes-framework/win-arm64/native/Microsoft.Graphics.ImagingInternal.winmd", "runtimes-framework/win-arm64/native/Microsoft.Graphics.Internal.Imaging.winmd", "runtimes-framework/win-arm64/native/Microsoft.Windows.AI.ContentModerationInternal.winmd", "runtimes-framework/win-arm64/native/Microsoft.Windows.AI.ContentSafety.dll", "runtimes-framework/win-arm64/native/Microsoft.Windows.AI.FoundationInternal.winmd", "runtimes-framework/win-arm64/native/Microsoft.Windows.AI.GenerativeInternal.winmd", "runtimes-framework/win-arm64/native/Microsoft.Windows.AI.Imaging.dll", "runtimes-framework/win-arm64/native/Microsoft.Windows.AI.Text.dll", "runtimes-framework/win-arm64/native/Microsoft.Windows.Internal.AI.ContentModeration.winmd", "runtimes-framework/win-arm64/native/Microsoft.Windows.Internal.AI.Generative.winmd", "runtimes-framework/win-arm64/native/Microsoft.Windows.Internal.Vision.winmd", "runtimes-framework/win-arm64/native/Microsoft.Windows.Private.Workloads.SessionManager.winmd", "runtimes-framework/win-arm64/native/Microsoft.Windows.PrivateCommon.winmd", "runtimes-framework/win-arm64/native/Microsoft.Windows.SemanticSearch.winmd", "runtimes-framework/win-arm64/native/Microsoft.Windows.Vision.winmd", "runtimes-framework/win-arm64/native/Microsoft.Windows.VisionInternal.winmd", "runtimes-framework/win-arm64/native/Microsoft.Windows.Workloads.Resources.dll", "runtimes-framework/win-arm64/native/Microsoft.Windows.Workloads.dll", "runtimes-framework/win-arm64/native/Microsoft.Windows.Workloads.pri", "runtimes-framework/win-arm64/native/NpuDetect/NPUDetect.dll", "runtimes-framework/win-arm64/native/SessionHandleIPCProxyStub.dll", "runtimes-framework/win-arm64/native/workloads.json", "runtimes-framework/win-arm64/native/workloads.qnn.json", "runtimes-framework/win-arm64ec/native/Microsoft.Graphics.Imaging.dll", "runtimes-framework/win-arm64ec/native/Microsoft.Graphics.ImagingInternal.ImageObjectRemover.winmd", "runtimes-framework/win-arm64ec/native/Microsoft.Graphics.ImagingInternal.winmd", "runtimes-framework/win-arm64ec/native/Microsoft.Graphics.Internal.Imaging.winmd", "runtimes-framework/win-arm64ec/native/Microsoft.Windows.AI.ContentModerationInternal.winmd", "runtimes-framework/win-arm64ec/native/Microsoft.Windows.AI.ContentSafety.dll", "runtimes-framework/win-arm64ec/native/Microsoft.Windows.AI.FoundationInternal.winmd", "runtimes-framework/win-arm64ec/native/Microsoft.Windows.AI.GenerativeInternal.winmd", "runtimes-framework/win-arm64ec/native/Microsoft.Windows.AI.Imaging.dll", "runtimes-framework/win-arm64ec/native/Microsoft.Windows.AI.Text.dll", "runtimes-framework/win-arm64ec/native/Microsoft.Windows.Internal.AI.ContentModeration.winmd", "runtimes-framework/win-arm64ec/native/Microsoft.Windows.Internal.AI.Generative.winmd", "runtimes-framework/win-arm64ec/native/Microsoft.Windows.Internal.Vision.winmd", "runtimes-framework/win-arm64ec/native/Microsoft.Windows.Private.Workloads.SessionManager.winmd", "runtimes-framework/win-arm64ec/native/Microsoft.Windows.PrivateCommon.winmd", "runtimes-framework/win-arm64ec/native/Microsoft.Windows.SemanticSearch.winmd", "runtimes-framework/win-arm64ec/native/Microsoft.Windows.Vision.winmd", "runtimes-framework/win-arm64ec/native/Microsoft.Windows.VisionInternal.winmd", "runtimes-framework/win-arm64ec/native/Microsoft.Windows.Workloads.Resources.dll", "runtimes-framework/win-arm64ec/native/Microsoft.Windows.Workloads.Resources_ec.dll", "runtimes-framework/win-arm64ec/native/Microsoft.Windows.Workloads.dll", "runtimes-framework/win-arm64ec/native/Microsoft.Windows.Workloads.pri", "runtimes-framework/win-arm64ec/native/NpuDetect/NPUDetect.dll", "runtimes-framework/win-arm64ec/native/SessionHandleIPCProxyStub.dll", "runtimes-framework/win-arm64ec/native/workloads.365.json", "runtimes-framework/win-arm64ec/native/workloads.json", "runtimes-framework/win-arm64ec/native/workloads.lnl.json", "runtimes-framework/win-arm64ec/native/workloads.qnn.json", "runtimes-framework/win-arm64ec/native/workloads.stx.json", "runtimes-framework/win-x64/native/Microsoft.Graphics.Imaging.dll", "runtimes-framework/win-x64/native/Microsoft.Graphics.ImagingInternal.ImageObjectRemover.winmd", "runtimes-framework/win-x64/native/Microsoft.Graphics.ImagingInternal.winmd", "runtimes-framework/win-x64/native/Microsoft.Graphics.Internal.Imaging.winmd", "runtimes-framework/win-x64/native/Microsoft.Windows.AI.ContentModerationInternal.winmd", "runtimes-framework/win-x64/native/Microsoft.Windows.AI.ContentSafety.dll", "runtimes-framework/win-x64/native/Microsoft.Windows.AI.FoundationInternal.winmd", "runtimes-framework/win-x64/native/Microsoft.Windows.AI.GenerativeInternal.winmd", "runtimes-framework/win-x64/native/Microsoft.Windows.AI.Imaging.dll", "runtimes-framework/win-x64/native/Microsoft.Windows.AI.Text.dll", "runtimes-framework/win-x64/native/Microsoft.Windows.Internal.AI.ContentModeration.winmd", "runtimes-framework/win-x64/native/Microsoft.Windows.Internal.AI.Generative.winmd", "runtimes-framework/win-x64/native/Microsoft.Windows.Internal.Vision.winmd", "runtimes-framework/win-x64/native/Microsoft.Windows.Private.Workloads.SessionManager.winmd", "runtimes-framework/win-x64/native/Microsoft.Windows.PrivateCommon.winmd", "runtimes-framework/win-x64/native/Microsoft.Windows.SemanticSearch.winmd", "runtimes-framework/win-x64/native/Microsoft.Windows.Vision.winmd", "runtimes-framework/win-x64/native/Microsoft.Windows.VisionInternal.winmd", "runtimes-framework/win-x64/native/Microsoft.Windows.Workloads.Resources.dll", "runtimes-framework/win-x64/native/Microsoft.Windows.Workloads.Resources_ec.dll", "runtimes-framework/win-x64/native/Microsoft.Windows.Workloads.dll", "runtimes-framework/win-x64/native/Microsoft.Windows.Workloads.pri", "runtimes-framework/win-x64/native/NpuDetect/NPUDetect.dll", "runtimes-framework/win-x64/native/SessionHandleIPCProxyStub.dll", "runtimes-framework/win-x64/native/workloads.365.json", "runtimes-framework/win-x64/native/workloads.json", "runtimes-framework/win-x64/native/workloads.lnl.json", "runtimes-framework/win-x64/native/workloads.qnn.json", "runtimes-framework/win-x64/native/workloads.stx.json"]}, "Microsoft.WindowsAppSDK.Base/1.8.250831001": {"sha512": "8LlfXBS2Hpw+OoVXViJmIOPXl0nMbqMaFR3j6+QHFNc62VULwPEcXiMRcP2WbV/+mtC7W2LH6yx6uu/Hrr9lVw==", "type": "package", "path": "microsoft.windowsappsdk.base/1.8.250831001", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "NOTICE.txt", "build/Microsoft.WindowsAppSDK.Base.props", "build/Microsoft.WindowsAppSDK.Base.targets", "build/Microsoft.WindowsAppSDK.SelfContained.targets", "build/Microsoft.WindowsAppSDK.SingleFile.targets", "build/Microsoft.WindowsAppSDK.SingleProject.targets", "build/Microsoft.WindowsAppSDK.arm64ec.targets", "build/native/Microsoft.WindowsAppSDK.Base.props", "build/native/Microsoft.WindowsAppSDK.Base.targets", "buildTransitive/Microsoft.WindowsAppSDK.Base.props", "buildTransitive/Microsoft.WindowsAppSDK.Base.targets", "buildTransitive/Microsoft.WindowsAppSDK.SelfContained.targets", "buildTransitive/Microsoft.WindowsAppSDK.SingleFile.targets", "buildTransitive/Microsoft.WindowsAppSDK.SingleProject.targets", "buildTransitive/Microsoft.WindowsAppSDK.arm64ec.targets", "buildTransitive/native/Microsoft.WindowsAppSDK.Base.props", "buildTransitive/native/Microsoft.WindowsAppSDK.Base.targets", "license.txt", "microsoft.windowsappsdk.base.1.8.250831001.nupkg.sha512", "microsoft.windowsappsdk.base.nuspec"]}, "Microsoft.WindowsAppSDK.DWrite/1.8.25090401": {"sha512": "WJ0p9yMgiNYqU2O5ZKCXcb7FBjryIUUopgeYMvnlf1yBUYgdjMFMkoJqYVqkz866wnntiB2IZhLxEzhFzvVs1A==", "type": "package", "path": "microsoft.windowsappsdk.dwrite/1.8.25090401", "files": [".nupkg.metadata", ".signature.p7s", "build/Microsoft.WindowsAppSDK.DWrite.ProjectCapabilities.props", "build/Microsoft.WindowsAppSDK.DWrite.props", "build/Microsoft.WindowsAppSDK.DWrite.targets", "buildTransitive/Microsoft.WindowsAppSDK.DWrite.ProjectCapabilities.props", "buildTransitive/Microsoft.WindowsAppSDK.DWrite.props", "buildTransitive/Microsoft.WindowsAppSDK.DWrite.targets", "include/dwrite.h", "include/dwrite_1.h", "include/dwrite_2.h", "include/dwrite_3.h", "include/dwrite_core.h", "lib/native/arm64/DWriteCore.lib", "lib/native/arm64ec/DWriteCore.lib", "lib/native/x64/DWriteCore.lib", "lib/native/x86/DWriteCore.lib", "license.txt", "microsoft.windowsappsdk.dwrite.1.8.25090401.nupkg.sha512", "microsoft.windowsappsdk.dwrite.nuspec", "runtimes-framework/win-arm64/native/DWriteCore.dll", "runtimes-framework/win-arm64ec/native/DWriteCore.dll", "runtimes-framework/win-x64/native/DWriteCore.dll", "runtimes-framework/win-x86/native/DWriteCore.dll"]}, "Microsoft.WindowsAppSDK.Foundation/1.8.250906002": {"sha512": "ltIXeHUX0AATpqmx/oBcRK+zhtK0KAfoGqItlQRlef9kG7Itj9iXAI+1EdFr4cQYzHzFM3PPLszEWDyR633svA==", "type": "package", "path": "microsoft.windowsappsdk.foundation/1.8.250906002", "files": [".nupkg.metadata", ".signature.p7s", "build/Microsoft.WindowsAppSDK.AutoInitializer.CS.targets", "build/Microsoft.WindowsAppSDK.AutoInitializerCommon.targets", "build/Microsoft.WindowsAppSDK.Bootstrap.CS.targets", "build/Microsoft.WindowsAppSDK.BootstrapCommon.targets", "build/Microsoft.WindowsAppSDK.CompatibilitySetter.CS.targets", "build/Microsoft.WindowsAppSDK.DeploymentManager.CS.targets", "build/Microsoft.WindowsAppSDK.DeploymentManagerCommon.targets", "build/Microsoft.WindowsAppSDK.Foundation.props", "build/Microsoft.WindowsAppSDK.Foundation.targets", "build/Microsoft.WindowsAppSDK.UndockedRegFreeWinRT.CS.targets", "build/Microsoft.WindowsAppSDK.UndockedRegFreeWinRTCommon.targets", "build/README.md", "build/native/Microsoft.WindowsAppSDK.Foundation.props", "build/native/Microsoft.WindowsAppSDK.Foundation.targets", "build/native/MrtCore.C.props", "build/native/MrtCore.props", "build/native/WindowsAppSDK-Nuget-Native.AutoInitializer.targets", "build/native/WindowsAppSDK-Nuget-Native.Bootstrap.targets", "build/native/WindowsAppSDK-Nuget-Native.C.props", "build/native/WindowsAppSDK-Nuget-Native.CompatibilitySetter.targets", "build/native/WindowsAppSDK-Nuget-Native.DeploymentManager.targets", "build/native/WindowsAppSDK-Nuget-Native.UndockedRegFreeWinRT.targets", "build/native/WindowsAppSDK-Nuget-Native.WinRt.props", "buildTransitive/Microsoft.WindowsAppSDK.AutoInitializer.CS.targets", "buildTransitive/Microsoft.WindowsAppSDK.AutoInitializerCommon.targets", "buildTransitive/Microsoft.WindowsAppSDK.Bootstrap.CS.targets", "buildTransitive/Microsoft.WindowsAppSDK.BootstrapCommon.targets", "buildTransitive/Microsoft.WindowsAppSDK.CompatibilitySetter.CS.targets", "buildTransitive/Microsoft.WindowsAppSDK.DeploymentManager.CS.targets", "buildTransitive/Microsoft.WindowsAppSDK.DeploymentManagerCommon.targets", "buildTransitive/Microsoft.WindowsAppSDK.Foundation.props", "buildTransitive/Microsoft.WindowsAppSDK.Foundation.targets", "buildTransitive/Microsoft.WindowsAppSDK.UndockedRegFreeWinRT.CS.targets", "buildTransitive/Microsoft.WindowsAppSDK.UndockedRegFreeWinRTCommon.targets", "buildTransitive/README.md", "buildTransitive/native/Microsoft.WindowsAppSDK.Foundation.props", "buildTransitive/native/Microsoft.WindowsAppSDK.Foundation.targets", "buildTransitive/native/MrtCore.C.props", "buildTransitive/native/MrtCore.props", "buildTransitive/native/WindowsAppSDK-Nuget-Native.AutoInitializer.targets", "buildTransitive/native/WindowsAppSDK-Nuget-Native.Bootstrap.targets", "buildTransitive/native/WindowsAppSDK-Nuget-Native.C.props", "buildTransitive/native/WindowsAppSDK-Nuget-Native.CompatibilitySetter.targets", "buildTransitive/native/WindowsAppSDK-Nuget-Native.DeploymentManager.targets", "buildTransitive/native/WindowsAppSDK-Nuget-Native.UndockedRegFreeWinRT.targets", "buildTransitive/native/WindowsAppSDK-Nuget-Native.WinRt.props", "include/DeploymentManagerAutoInitializer.cpp", "include/DeploymentManagerAutoInitializer.cs", "include/MRM.h", "include/MddBootstrap.h", "include/MddBootstrapAutoInitializer.cpp", "include/MddBootstrapAutoInitializer.cs", "include/Microsoft.Windows.ApplicationModel.Resources.idl", "include/MsixDynamicDependency.h", "include/Security.AccessControl.h", "include/UndockedRegFreeWinRT-AutoInitializer.cpp", "include/UndockedRegFreeWinRT-AutoInitializer.cs", "include/WindowsAppRuntimeAutoInitializer.cpp", "include/WindowsAppRuntimeAutoInitializer.cs", "include/WindowsAppRuntimeInsights.h", "include/decimal.h", "include/decimalcppwinrt.h", "include/wil_msixdynamicdependency.h", "lib/native/arm64/MRM.lib", "lib/native/arm64/Microsoft.WindowsAppRuntime.Bootstrap.lib", "lib/native/arm64/Microsoft.WindowsAppRuntime.lib", "lib/native/arm64ec/MRM.lib", "lib/native/arm64ec/Microsoft.WindowsAppRuntime.Bootstrap.lib", "lib/native/arm64ec/Microsoft.WindowsAppRuntime.lib", "lib/native/x64/MRM.lib", "lib/native/x64/Microsoft.WindowsAppRuntime.Bootstrap.lib", "lib/native/x64/Microsoft.WindowsAppRuntime.lib", "lib/native/x86/MRM.lib", "lib/native/x86/Microsoft.WindowsAppRuntime.Bootstrap.lib", "lib/native/x86/Microsoft.WindowsAppRuntime.lib", "lib/net6.0-windows10.0.17763.0/Microsoft.Security.Authentication.OAuth.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppLifecycle.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppLifecycle.Projection.xml", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppNotifications.Builder.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppNotifications.Builder.Projection.xml", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppNotifications.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppNotifications.Projection.xml", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.Background.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.xml", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.Resources.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.Resources.Projection.xml", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.xml", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.BadgeNotifications.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Foundation.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Management.Deployment.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Management.Deployment.Projection.xml", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Media.Capture.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.PushNotifications.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.PushNotifications.Projection.xml", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Security.AccessControl.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Security.AccessControl.Projection.xml", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Storage.Pickers.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Storage.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Storage.Projection.xml", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.System.Power.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.System.Power.Projection.xml", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.System.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.System.Projection.xml", "lib/net6.0-windows10.0.17763.0/Microsoft.WindowsAppRuntime.Bootstrap.Net.dll", "license.txt", "metadata/Microsoft.Security.Authentication.OAuth.winmd", "metadata/Microsoft.Windows.AppLifecycle.winmd", "metadata/Microsoft.Windows.AppLifecycle.xml", "metadata/Microsoft.Windows.AppNotifications.Builder.winmd", "metadata/Microsoft.Windows.AppNotifications.Builder.xml", "metadata/Microsoft.Windows.AppNotifications.winmd", "metadata/Microsoft.Windows.AppNotifications.xml", "metadata/Microsoft.Windows.ApplicationModel.Background.UniversalBGTask.winmd", "metadata/Microsoft.Windows.ApplicationModel.Background.winmd", "metadata/Microsoft.Windows.ApplicationModel.DynamicDependency.winmd", "metadata/Microsoft.Windows.ApplicationModel.DynamicDependency.xml", "metadata/Microsoft.Windows.ApplicationModel.Resources.winmd", "metadata/Microsoft.Windows.ApplicationModel.Resources.xml", "metadata/Microsoft.Windows.ApplicationModel.WindowsAppRuntime.winmd", "metadata/Microsoft.Windows.ApplicationModel.WindowsAppRuntime.xml", "metadata/Microsoft.Windows.BadgeNotifications.winmd", "metadata/Microsoft.Windows.Foundation.winmd", "metadata/Microsoft.Windows.Globalization.winmd", "metadata/Microsoft.Windows.Globalization.xml", "metadata/Microsoft.Windows.Management.Deployment.winmd", "metadata/Microsoft.Windows.Management.Deployment.xml", "metadata/Microsoft.Windows.Media.Capture.winmd", "metadata/Microsoft.Windows.PushNotifications.winmd", "metadata/Microsoft.Windows.PushNotifications.xml", "metadata/Microsoft.Windows.Security.AccessControl.winmd", "metadata/Microsoft.Windows.Security.AccessControl.xml", "metadata/Microsoft.Windows.Storage.Pickers.winmd", "metadata/Microsoft.Windows.Storage.winmd", "metadata/Microsoft.Windows.Storage.xml", "metadata/Microsoft.Windows.System.Power.winmd", "metadata/Microsoft.Windows.System.Power.xml", "metadata/Microsoft.Windows.System.winmd", "metadata/Microsoft.Windows.System.xml", "microsoft.windowsappsdk.foundation.1.8.250906002.nupkg.sha512", "microsoft.windowsappsdk.foundation.nuspec", "runtimes-framework/package.appxfragment", "runtimes-framework/win-arm64/native/DeploymentAgent.exe", "runtimes-framework/win-arm64/native/MRM.dll", "runtimes-framework/win-arm64/native/Microsoft.Windows.ApplicationModel.Resources.dll", "runtimes-framework/win-arm64/native/Microsoft.WindowsAppRuntime.dll", "runtimes-framework/win-arm64/native/Microsoft.WindowsAppRuntime.pri", "runtimes-framework/win-arm64/native/PushNotificationsLongRunningTask.ProxyStub.dll", "runtimes-framework/win-arm64/native/RestartAgent.exe", "runtimes-framework/win-arm64ec/native/DeploymentAgent.exe", "runtimes-framework/win-arm64ec/native/MRM.dll", "runtimes-framework/win-arm64ec/native/Microsoft.Windows.ApplicationModel.Resources.dll", "runtimes-framework/win-arm64ec/native/Microsoft.WindowsAppRuntime.dll", "runtimes-framework/win-arm64ec/native/Microsoft.WindowsAppRuntime.pri", "runtimes-framework/win-arm64ec/native/PushNotificationsLongRunningTask.ProxyStub.dll", "runtimes-framework/win-arm64ec/native/RestartAgent.exe", "runtimes-framework/win-x64/native/DeploymentAgent.exe", "runtimes-framework/win-x64/native/MRM.dll", "runtimes-framework/win-x64/native/Microsoft.Windows.ApplicationModel.Resources.dll", "runtimes-framework/win-x64/native/Microsoft.WindowsAppRuntime.dll", "runtimes-framework/win-x64/native/Microsoft.WindowsAppRuntime.pri", "runtimes-framework/win-x64/native/PushNotificationsLongRunningTask.ProxyStub.dll", "runtimes-framework/win-x64/native/RestartAgent.exe", "runtimes-framework/win-x86/native/DeploymentAgent.exe", "runtimes-framework/win-x86/native/MRM.dll", "runtimes-framework/win-x86/native/Microsoft.Windows.ApplicationModel.Resources.dll", "runtimes-framework/win-x86/native/Microsoft.WindowsAppRuntime.dll", "runtimes-framework/win-x86/native/Microsoft.WindowsAppRuntime.pri", "runtimes-framework/win-x86/native/PushNotificationsLongRunningTask.ProxyStub.dll", "runtimes-framework/win-x86/native/RestartAgent.exe", "runtimes/win-arm64/native/Microsoft.Windows.ApplicationModel.Background.UniversalBGTask.dll", "runtimes/win-arm64/native/Microsoft.WindowsAppRuntime.Bootstrap.dll", "runtimes/win-arm64ec/native/Microsoft.Windows.ApplicationModel.Background.UniversalBGTask.dll", "runtimes/win-arm64ec/native/Microsoft.WindowsAppRuntime.Bootstrap.dll", "runtimes/win-x64/native/Microsoft.Windows.ApplicationModel.Background.UniversalBGTask.dll", "runtimes/win-x64/native/Microsoft.WindowsAppRuntime.Bootstrap.dll", "runtimes/win-x86/native/Microsoft.Windows.ApplicationModel.Background.UniversalBGTask.dll", "runtimes/win-x86/native/Microsoft.WindowsAppRuntime.Bootstrap.dll"]}, "Microsoft.WindowsAppSDK.InteractiveExperiences/1.8.250906004": {"sha512": "UoK2yeZiycD1DmADHZz+hcMAoOaUfXLc9qUPfOjmVeKQ6i5ghGMjx/nd49bksP3wVhmSGHxb3argRKWPkK5maw==", "type": "package", "path": "microsoft.windowsappsdk.interactiveexperiences/1.8.250906004", "files": [".nupkg.metadata", ".signature.p7s", "build/Microsoft.InteractiveExperiences.Capabilities.props", "build/Microsoft.InteractiveExperiences.Capabilities.targets", "build/Microsoft.InteractiveExperiences.Common.props", "build/Microsoft.InteractiveExperiences.Common.targets", "build/Microsoft.InteractiveExperiences.EC.Capabilities.props", "build/Microsoft.InteractiveExperiences.EC.Capabilities.targets", "build/Microsoft.InteractiveExperiences.EC.Common.props", "build/Microsoft.InteractiveExperiences.EC.Common.targets", "build/Microsoft.InteractiveExperiences.EC.props", "build/Microsoft.InteractiveExperiences.EC.targets", "build/Microsoft.InteractiveExperiences.props", "build/Microsoft.InteractiveExperiences.targets", "build/Microsoft.WindowsAppSDK.InteractiveExperiences.props", "build/Microsoft.WindowsAppSDK.InteractiveExperiences.targets", "build/native/Microsoft.InteractiveExperiences.EC.props", "build/native/Microsoft.InteractiveExperiences.EC.targets", "build/native/Microsoft.InteractiveExperiences.props", "build/native/Microsoft.InteractiveExperiences.targets", "build/native/Microsoft.WindowsAppSDK.InteractiveExperiences.props", "build/native/Microsoft.WindowsAppSDK.InteractiveExperiences.targets", "buildTransitive/Microsoft.InteractiveExperiences.Capabilities.props", "buildTransitive/Microsoft.InteractiveExperiences.Capabilities.targets", "buildTransitive/Microsoft.InteractiveExperiences.Common.props", "buildTransitive/Microsoft.InteractiveExperiences.Common.targets", "buildTransitive/Microsoft.InteractiveExperiences.EC.Capabilities.props", "buildTransitive/Microsoft.InteractiveExperiences.EC.Capabilities.targets", "buildTransitive/Microsoft.InteractiveExperiences.EC.Common.props", "buildTransitive/Microsoft.InteractiveExperiences.EC.Common.targets", "buildTransitive/Microsoft.InteractiveExperiences.EC.props", "buildTransitive/Microsoft.InteractiveExperiences.EC.targets", "buildTransitive/Microsoft.InteractiveExperiences.props", "buildTransitive/Microsoft.InteractiveExperiences.targets", "buildTransitive/Microsoft.WindowsAppSDK.InteractiveExperiences.props", "buildTransitive/Microsoft.WindowsAppSDK.InteractiveExperiences.targets", "buildTransitive/native/Microsoft.InteractiveExperiences.EC.props", "buildTransitive/native/Microsoft.InteractiveExperiences.EC.targets", "buildTransitive/native/Microsoft.InteractiveExperiences.props", "buildTransitive/native/Microsoft.InteractiveExperiences.targets", "buildTransitive/native/Microsoft.WindowsAppSDK.InteractiveExperiences.props", "buildTransitive/native/Microsoft.WindowsAppSDK.InteractiveExperiences.targets", "include/Microsoft.UI.Composition.Interop.h", "include/Microsoft.UI.Designer.Interop.h", "include/Microsoft.UI.Dispatching.Interop.h", "include/Microsoft.UI.Input.InputCursor.Interop.h", "include/Microsoft.UI.Input.InputPreTranslateSource.Interop.h", "include/Microsoft.UI.Interop.h", "include/winrt/Microsoft.UI.Composition.Interop.h", "include/winrt/Microsoft.UI.Designer.Interop.h", "include/winrt/Microsoft.UI.Dispatching.Interop.h", "include/winrt/Microsoft.UI.Input.InputCursor.Interop.h", "include/winrt/Microsoft.UI.Input.InputPreTranslateSource.Interop.h", "include/winrt/Microsoft.UI.Interop.h", "lib/native/win10-arm64/Microsoft.UI.Dispatching.lib", "lib/native/win10-arm64ec/Microsoft.UI.Dispatching.lib", "lib/native/win10-x64/Microsoft.UI.Dispatching.lib", "lib/native/win10-x86/Microsoft.UI.Dispatching.lib", "lib/net6.0-windows10.0.17763.0/Microsoft.InteractiveExperiences.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.InteractiveExperiences.Projection.xml", "lib/net6.0-windows10.0.18362.0/Microsoft.InteractiveExperiences.Projection.dll", "lib/net6.0-windows10.0.18362.0/Microsoft.InteractiveExperiences.Projection.xml", "license.txt", "metadata/10.0.17763.0/Microsoft.Foundation.winmd", "metadata/10.0.17763.0/Microsoft.Graphics.winmd", "metadata/10.0.17763.0/Microsoft.Graphics.xml", "metadata/10.0.17763.0/Microsoft.UI.winmd", "metadata/10.0.17763.0/Microsoft.UI.xml", "metadata/10.0.18362.0/Microsoft.Foundation.winmd", "metadata/10.0.18362.0/Microsoft.Graphics.winmd", "metadata/10.0.18362.0/Microsoft.Graphics.xml", "metadata/10.0.18362.0/Microsoft.UI.winmd", "metadata/10.0.18362.0/Microsoft.UI.xml", "microsoft.windowsappsdk.interactiveexperiences.1.8.250906004.nupkg.sha512", "microsoft.windowsappsdk.interactiveexperiences.nuspec", "runtimes-framework/package.appxfragment", "runtimes-framework/win-arm64/native/CoreMessagingXP.dll", "runtimes-framework/win-arm64/native/DwmSceneI.dll", "runtimes-framework/win-arm64/native/Microsoft.DirectManipulation.dll", "runtimes-framework/win-arm64/native/Microsoft.Graphics.Display.dll", "runtimes-framework/win-arm64/native/Microsoft.InputStateManager.dll", "runtimes-framework/win-arm64/native/Microsoft.Internal.FrameworkUdk.dll", "runtimes-framework/win-arm64/native/Microsoft.UI.Composition.OSSupport.dll", "runtimes-framework/win-arm64/native/Microsoft.UI.Designer.dll", "runtimes-framework/win-arm64/native/Microsoft.UI.Input.dll", "runtimes-framework/win-arm64/native/Microsoft.UI.Windowing.Core.dll", "runtimes-framework/win-arm64/native/Microsoft.UI.Windowing.dll", "runtimes-framework/win-arm64/native/Microsoft.UI.dll", "runtimes-framework/win-arm64/native/Microsoft.UI.pri", "runtimes-framework/win-arm64/native/dcompi.dll", "runtimes-framework/win-arm64/native/dwmcorei.dll", "runtimes-framework/win-arm64/native/marshal.dll", "runtimes-framework/win-arm64/native/wuceffectsi.dll", "runtimes-framework/win-arm64ec/native/CoreMessagingXP.dll", "runtimes-framework/win-arm64ec/native/DwmSceneI.dll", "runtimes-framework/win-arm64ec/native/Microsoft.DirectManipulation.dll", "runtimes-framework/win-arm64ec/native/Microsoft.Graphics.Display.dll", "runtimes-framework/win-arm64ec/native/Microsoft.InputStateManager.dll", "runtimes-framework/win-arm64ec/native/Microsoft.Internal.FrameworkUdk.dll", "runtimes-framework/win-arm64ec/native/Microsoft.UI.Composition.OSSupport.dll", "runtimes-framework/win-arm64ec/native/Microsoft.UI.Designer.dll", "runtimes-framework/win-arm64ec/native/Microsoft.UI.Input.dll", "runtimes-framework/win-arm64ec/native/Microsoft.UI.Windowing.Core.dll", "runtimes-framework/win-arm64ec/native/Microsoft.UI.Windowing.dll", "runtimes-framework/win-arm64ec/native/Microsoft.UI.dll", "runtimes-framework/win-arm64ec/native/Microsoft.UI.pri", "runtimes-framework/win-arm64ec/native/dcompi.dll", "runtimes-framework/win-arm64ec/native/dwmcorei.dll", "runtimes-framework/win-arm64ec/native/marshal.dll", "runtimes-framework/win-arm64ec/native/wuceffectsi.dll", "runtimes-framework/win-x64/native/CoreMessagingXP.dll", "runtimes-framework/win-x64/native/DwmSceneI.dll", "runtimes-framework/win-x64/native/Microsoft.DirectManipulation.dll", "runtimes-framework/win-x64/native/Microsoft.Graphics.Display.dll", "runtimes-framework/win-x64/native/Microsoft.InputStateManager.dll", "runtimes-framework/win-x64/native/Microsoft.Internal.FrameworkUdk.dll", "runtimes-framework/win-x64/native/Microsoft.UI.Composition.OSSupport.dll", "runtimes-framework/win-x64/native/Microsoft.UI.Designer.dll", "runtimes-framework/win-x64/native/Microsoft.UI.Input.dll", "runtimes-framework/win-x64/native/Microsoft.UI.Windowing.Core.dll", "runtimes-framework/win-x64/native/Microsoft.UI.Windowing.dll", "runtimes-framework/win-x64/native/Microsoft.UI.dll", "runtimes-framework/win-x64/native/Microsoft.UI.pri", "runtimes-framework/win-x64/native/dcompi.dll", "runtimes-framework/win-x64/native/dwmcorei.dll", "runtimes-framework/win-x64/native/marshal.dll", "runtimes-framework/win-x64/native/wuceffectsi.dll", "runtimes-framework/win-x86/native/CoreMessagingXP.dll", "runtimes-framework/win-x86/native/DwmSceneI.dll", "runtimes-framework/win-x86/native/Microsoft.DirectManipulation.dll", "runtimes-framework/win-x86/native/Microsoft.Graphics.Display.dll", "runtimes-framework/win-x86/native/Microsoft.InputStateManager.dll", "runtimes-framework/win-x86/native/Microsoft.Internal.FrameworkUdk.dll", "runtimes-framework/win-x86/native/Microsoft.UI.Composition.OSSupport.dll", "runtimes-framework/win-x86/native/Microsoft.UI.Designer.dll", "runtimes-framework/win-x86/native/Microsoft.UI.Input.dll", "runtimes-framework/win-x86/native/Microsoft.UI.Windowing.Core.dll", "runtimes-framework/win-x86/native/Microsoft.UI.Windowing.dll", "runtimes-framework/win-x86/native/Microsoft.UI.dll", "runtimes-framework/win-x86/native/Microsoft.UI.pri", "runtimes-framework/win-x86/native/dcompi.dll", "runtimes-framework/win-x86/native/dwmcorei.dll", "runtimes-framework/win-x86/native/marshal.dll", "runtimes-framework/win-x86/native/wuceffectsi.dll"]}, "Microsoft.WindowsAppSDK.Runtime/1.8.250907003": {"sha512": "URsthdat9pv1wnRNAy0WA5yejsc47QsSjjJ+L6INEgIFilrp4/LYndpHkoWh3KwBSjwkskvZlSprbOl09YVg/g==", "type": "package", "path": "microsoft.windowsappsdk.runtime/1.8.250907003", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "NOTICE.txt", "WindowsAppSDK-VersionInfo.json", "WindowsAppSDK-VersionInfo.xml", "build/Microsoft.WindowsAppSDK.AppXReference.props", "build/Microsoft.WindowsAppSDK.ComponentReference.targets", "build/Microsoft.WindowsAppSDK.Runtime.props", "build/Microsoft.WindowsAppSDK.Runtime.targets", "build/MicrosoftWindowsAppSDKFoundationAppXVersion.props", "build/native/Microsoft.WindowsAppSDK.Runtime.props", "build/native/Microsoft.WindowsAppSDK.Runtime.targets", "buildTransitive/Microsoft.WindowsAppSDK.AppXReference.props", "buildTransitive/Microsoft.WindowsAppSDK.ComponentReference.targets", "buildTransitive/Microsoft.WindowsAppSDK.Runtime.props", "buildTransitive/Microsoft.WindowsAppSDK.Runtime.targets", "buildTransitive/MicrosoftWindowsAppSDKFoundationAppXVersion.props", "buildTransitive/native/Microsoft.WindowsAppSDK.Runtime.props", "buildTransitive/native/Microsoft.WindowsAppSDK.Runtime.targets", "include/WindowsAppSDK-VersionInfo.cs", "include/WindowsAppSDK-VersionInfo.h", "license.txt", "microsoft.windowsappsdk.runtime.1.8.250907003.nupkg.sha512", "microsoft.windowsappsdk.runtime.nuspec", "tools/MSIX/win10-arm64/MSIX.inventory", "tools/MSIX/win10-arm64/Microsoft.WindowsAppRuntime.1.8.msix", "tools/MSIX/win10-arm64/Microsoft.WindowsAppRuntime.DDLM.1.8.msix", "tools/MSIX/win10-arm64/Microsoft.WindowsAppRuntime.Main.1.8.msix", "tools/MSIX/win10-arm64/Microsoft.WindowsAppRuntime.Singleton.1.8.msix", "tools/MSIX/win10-arm64ec/MSIX.inventory", "tools/MSIX/win10-arm64ec/Microsoft.WindowsAppRuntime.1.8.msix", "tools/MSIX/win10-x64/MSIX.inventory", "tools/MSIX/win10-x64/Microsoft.WindowsAppRuntime.1.8.msix", "tools/MSIX/win10-x64/Microsoft.WindowsAppRuntime.DDLM.1.8.msix", "tools/MSIX/win10-x64/Microsoft.WindowsAppRuntime.Main.1.8.msix", "tools/MSIX/win10-x64/Microsoft.WindowsAppRuntime.Singleton.1.8.msix", "tools/MSIX/win10-x86/MSIX.inventory", "tools/MSIX/win10-x86/Microsoft.WindowsAppRuntime.1.8.msix", "tools/MSIX/win10-x86/Microsoft.WindowsAppRuntime.DDLM.1.8.msix", "tools/MSIX/win10-x86/Microsoft.WindowsAppRuntime.Main.1.8.msix", "tools/MSIX/win10-x86/Microsoft.WindowsAppRuntime.Singleton.1.8.msix"]}, "Microsoft.WindowsAppSDK.Widgets/1.8.250904007": {"sha512": "sgwdXYhb8S4JjBmWWiFxALT1xK0fJeAbisolctmodMX7tlvBXDgUyvl/GHfTQ61DGIiW+kokX61WR46L2YlhAA==", "type": "package", "path": "microsoft.windowsappsdk.widgets/1.8.250904007", "files": [".nupkg.metadata", ".signature.p7s", "build/Microsoft.WindowsAppSDK.Widgets.props", "build/Microsoft.WindowsAppSDK.Widgets.targets", "build/native/Microsoft.WindowsAppSDK.Widgets.props", "build/native/Microsoft.WindowsAppSDK.Widgets.targets", "buildTransitive/Microsoft.WindowsAppSDK.Widgets.props", "buildTransitive/Microsoft.WindowsAppSDK.Widgets.targets", "buildTransitive/native/Microsoft.WindowsAppSDK.Widgets.props", "buildTransitive/native/Microsoft.WindowsAppSDK.Widgets.targets", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Widgets.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Widgets.Projection.xml", "license.txt", "metadata/Microsoft.Windows.Widgets.winmd", "metadata/Microsoft.Windows.Widgets.xml", "microsoft.windowsappsdk.widgets.1.8.250904007.nupkg.sha512", "microsoft.windowsappsdk.widgets.nuspec", "runtimes-framework/package.appxfragment", "runtimes-framework/win-arm64/native/Microsoft.Windows.Widgets.dll", "runtimes-framework/win-arm64ec/native/Microsoft.Windows.Widgets.dll", "runtimes-framework/win-x64/native/Microsoft.Windows.Widgets.dll", "runtimes-framework/win-x86/native/Microsoft.Windows.Widgets.dll"]}, "Microsoft.WindowsAppSDK.WinUI/1.8.250906003": {"sha512": "6oskwUluqlDGwUcwYlY3GWTMLajyjh9e790SmWzCCMDRV6sunYbqp7DkiSLzn8nhgSbGvmj6zG92JnkYRlbrXw==", "type": "package", "path": "microsoft.windowsappsdk.winui/1.8.250906003", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "NOTICE.txt", "build/Microsoft.UI.Xaml.Markup.Compiler.BeforeCommon.targets", "build/Microsoft.UI.Xaml.Markup.Compiler.interop.targets", "build/Microsoft.UI.Xaml.Markup.Compiler.props", "build/Microsoft.UI.Xaml.Markup.Compiler.targets", "build/Microsoft.WinUI.AppX.targets", "build/Microsoft.WinUI.NET.Markup.Compiler.targets", "build/Microsoft.WinUI.ProjectCapabilities.props", "build/Microsoft.WinUI.References.targets", "build/Microsoft.WinUI.props", "build/Microsoft.WinUI.targets", "build/Microsoft.WindowsAppSDK.WinUI.props", "build/Microsoft.WindowsAppSDK.WinUI.targets", "build/native/LiftedWinRTClassRegistrations.xml", "build/native/Microsoft.WinUI.References.targets", "build/native/Microsoft.WinUI.props", "build/native/Microsoft.WinUI.targets", "build/native/Microsoft.WindowsAppSDK.WinUI.props", "build/native/Microsoft.WindowsAppSDK.WinUI.targets", "buildTransitive/Microsoft.UI.Xaml.Markup.Compiler.BeforeCommon.targets", "buildTransitive/Microsoft.UI.Xaml.Markup.Compiler.interop.targets", "buildTransitive/Microsoft.UI.Xaml.Markup.Compiler.props", "buildTransitive/Microsoft.UI.Xaml.Markup.Compiler.targets", "buildTransitive/Microsoft.WinUI.AppX.targets", "buildTransitive/Microsoft.WinUI.NET.Markup.Compiler.targets", "buildTransitive/Microsoft.WinUI.ProjectCapabilities.props", "buildTransitive/Microsoft.WinUI.References.targets", "buildTransitive/Microsoft.WinUI.props", "buildTransitive/Microsoft.WinUI.targets", "buildTransitive/Microsoft.WindowsAppSDK.WinUI.props", "buildTransitive/Microsoft.WindowsAppSDK.WinUI.targets", "buildTransitive/native/LiftedWinRTClassRegistrations.xml", "buildTransitive/native/Microsoft.WinUI.References.targets", "buildTransitive/native/Microsoft.WinUI.props", "buildTransitive/native/Microsoft.WinUI.targets", "buildTransitive/native/Microsoft.WindowsAppSDK.WinUI.props", "buildTransitive/native/Microsoft.WindowsAppSDK.WinUI.targets", "include/microsoft.ui.xaml.hosting.referencetracker.h", "include/microsoft.ui.xaml.hosting.referencetracker.idl", "include/microsoft.ui.xaml.media.dxinterop.h", "include/microsoft.ui.xaml.media.dxinterop.idl", "include/microsoft.ui.xaml.window.h", "include/microsoft.ui.xaml.window.idl", "include/winrtdirect3d11.h", "include/winrtdirectxcommon.h", "include/xamlom.winui.h", "include/xamlom.winui.idl", "lib/native/Microsoft.UI/Themes/generic.xaml", "lib/net6.0-windows10.0.17763.0/Microsoft.WinUI.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.WinUI.xml", "lib/net6.0-windows10.0.17763.0/Microsoft.WinUI/Themes/generic.xaml", "license.txt", "metadata/Microsoft.UI.Text.winmd", "metadata/Microsoft.UI.Text.xml", "metadata/Microsoft.UI.Xaml.winmd", "metadata/Microsoft.UI.Xaml.xml", "microsoft.windowsappsdk.winui.1.8.250906003.nupkg.sha512", "microsoft.windowsappsdk.winui.nuspec", "runtimes-framework/package.appxfragment", "runtimes-framework/win-arm64/native/Microsoft.UI.Xaml.Controls.dll", "runtimes-framework/win-arm64/native/Microsoft.UI.Xaml.Controls.pri", "runtimes-framework/win-arm64/native/Microsoft.UI.Xaml.Internal.dll", "runtimes-framework/win-arm64/native/Microsoft.UI.Xaml.Phone.dll", "runtimes-framework/win-arm64/native/Microsoft.UI.Xaml/Assets/NoiseAsset_256x256_PNG.png", "runtimes-framework/win-arm64/native/Microsoft.UI.Xaml/Assets/map.html", "runtimes-framework/win-arm64/native/Microsoft.ui.xaml.dll", "runtimes-framework/win-arm64/native/Microsoft.ui.xaml.resources.19h1.dll", "runtimes-framework/win-arm64/native/Microsoft.ui.xaml.resources.common.dll", "runtimes-framework/win-arm64/native/WinUIEdit.dll", "runtimes-framework/win-arm64/native/af-ZA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/af-ZA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/am-ET/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/am-ET/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/ar-SA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/ar-SA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/as-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/as-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/az-Latn-AZ/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/az-Latn-AZ/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/bg-BG/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/bg-BG/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/bn-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/bn-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/bs-Latn-BA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/bs-Latn-BA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/ca-ES/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/ca-ES/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/ca-Es-VALENCIA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/ca-Es-VALENCIA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/cs-CZ/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/cs-CZ/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/cy-GB/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/cy-GB/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/da-DK/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/da-DK/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/de-DE/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/de-DE/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/el-GR/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/el-GR/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/en-GB/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/en-GB/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/en-us/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/en-us/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/es-ES/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/es-ES/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/es-MX/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/es-MX/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/et-EE/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/et-EE/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/eu-ES/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/eu-ES/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/fa-IR/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/fa-IR/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/fi-FI/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/fi-FI/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/fil-PH/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/fil-PH/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/fr-CA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/fr-CA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/fr-FR/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/fr-FR/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/ga-IE/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/ga-IE/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/gd-gb/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/gd-gb/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/gl-ES/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/gl-ES/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/gu-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/gu-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/he-IL/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/he-IL/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/hi-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/hi-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/hr-HR/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/hr-HR/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/hu-HU/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/hu-HU/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/hy-AM/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/hy-AM/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/id-ID/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/id-ID/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/is-IS/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/is-IS/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/it-IT/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/it-IT/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/ja-JP/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/ja-JP/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/ka-GE/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/ka-GE/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/kk-KZ/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/kk-KZ/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/km-KH/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/km-KH/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/kn-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/kn-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/ko-KR/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/ko-KR/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/kok-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/kok-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/lb-LU/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/lb-LU/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/lo-LA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/lo-LA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/lt-LT/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/lt-LT/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/lv-LV/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/lv-LV/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/mi-NZ/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/mi-NZ/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/mk-MK/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/mk-MK/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/ml-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/ml-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/mr-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/mr-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/ms-MY/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/ms-MY/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/mt-MT/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/mt-MT/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/nb-NO/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/nb-NO/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/ne-NP/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/ne-NP/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/nl-NL/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/nl-NL/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/nn-NO/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/nn-NO/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/or-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/or-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/pa-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/pa-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/pl-PL/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/pl-PL/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/pt-BR/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/pt-BR/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/pt-PT/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/pt-PT/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/quz-PE/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/quz-PE/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/ro-RO/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/ro-RO/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/ru-RU/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/ru-RU/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/sk-SK/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/sk-SK/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/sl-SI/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/sl-SI/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/sq-AL/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/sq-AL/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/sr-Cyrl-BA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/sr-Cyrl-BA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/sr-Cyrl-RS/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/sr-Cyrl-RS/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/sr-Latn-RS/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/sr-Latn-RS/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/sv-SE/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/sv-SE/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/ta-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/ta-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/te-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/te-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/th-TH/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/th-TH/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/tr-TR/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/tr-TR/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/tt-RU/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/tt-RU/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/ug-CN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/ug-CN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/uk-UA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/uk-UA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/ur-PK/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/ur-PK/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/uz-Latn-UZ/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/uz-Latn-UZ/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/vi-VN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/vi-VN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/zh-CN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/zh-CN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/zh-TW/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/zh-TW/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/Microsoft.UI.Xaml.Controls.dll", "runtimes-framework/win-arm64ec/native/Microsoft.UI.Xaml.Controls.pri", "runtimes-framework/win-arm64ec/native/Microsoft.UI.Xaml.Internal.dll", "runtimes-framework/win-arm64ec/native/Microsoft.UI.Xaml.Phone.dll", "runtimes-framework/win-arm64ec/native/Microsoft.UI.Xaml/Assets/NoiseAsset_256x256_PNG.png", "runtimes-framework/win-arm64ec/native/Microsoft.UI.Xaml/Assets/map.html", "runtimes-framework/win-arm64ec/native/Microsoft.ui.xaml.dll", "runtimes-framework/win-arm64ec/native/Microsoft.ui.xaml.resources.19h1.dll", "runtimes-framework/win-arm64ec/native/Microsoft.ui.xaml.resources.common.dll", "runtimes-framework/win-arm64ec/native/WinUIEdit.dll", "runtimes-framework/win-arm64ec/native/af-ZA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/af-ZA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/am-ET/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/am-ET/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/ar-SA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/ar-SA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/as-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/as-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/az-Latn-AZ/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/az-Latn-AZ/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/bg-BG/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/bg-BG/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/bn-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/bn-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/bs-Latn-BA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/bs-Latn-BA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/ca-ES/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/ca-ES/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/ca-Es-VALENCIA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/ca-Es-VALENCIA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/cs-CZ/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/cs-CZ/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/cy-GB/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/cy-GB/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/da-DK/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/da-DK/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/de-DE/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/de-DE/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/el-GR/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/el-GR/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/en-GB/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/en-GB/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/en-us/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/en-us/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/es-ES/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/es-ES/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/es-MX/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/es-MX/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/et-EE/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/et-EE/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/eu-ES/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/eu-ES/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/fa-IR/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/fa-IR/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/fi-FI/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/fi-FI/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/fil-PH/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/fil-PH/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/fr-CA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/fr-CA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/fr-FR/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/fr-FR/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/ga-IE/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/ga-IE/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/gd-gb/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/gd-gb/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/gl-ES/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/gl-ES/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/gu-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/gu-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/he-IL/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/he-IL/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/hi-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/hi-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/hr-HR/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/hr-HR/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/hu-HU/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/hu-HU/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/hy-AM/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/hy-AM/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/id-ID/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/id-ID/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/is-IS/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/is-IS/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/it-IT/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/it-IT/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/ja-JP/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/ja-JP/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/ka-GE/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/ka-GE/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/kk-KZ/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/kk-KZ/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/km-KH/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/km-KH/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/kn-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/kn-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/ko-KR/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/ko-KR/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/kok-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/kok-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/lb-LU/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/lb-LU/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/lo-LA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/lo-LA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/lt-LT/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/lt-LT/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/lv-LV/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/lv-LV/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/mi-NZ/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/mi-NZ/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/mk-MK/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/mk-MK/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/ml-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/ml-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/mr-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/mr-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/ms-MY/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/ms-MY/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/mt-MT/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/mt-MT/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/nb-NO/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/nb-NO/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/ne-NP/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/ne-NP/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/nl-NL/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/nl-NL/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/nn-NO/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/nn-NO/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/or-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/or-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/pa-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/pa-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/pl-PL/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/pl-PL/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/pt-BR/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/pt-BR/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/pt-PT/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/pt-PT/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/quz-PE/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/quz-PE/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/ro-RO/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/ro-RO/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/ru-RU/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/ru-RU/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/sk-SK/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/sk-SK/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/sl-SI/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/sl-SI/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/sq-AL/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/sq-AL/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/sr-Cyrl-BA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/sr-Cyrl-BA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/sr-Cyrl-RS/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/sr-Cyrl-RS/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/sr-Latn-RS/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/sr-Latn-RS/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/sv-SE/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/sv-SE/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/ta-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/ta-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/te-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/te-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/th-TH/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/th-TH/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/tr-TR/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/tr-TR/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/tt-RU/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/tt-RU/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/ug-CN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/ug-CN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/uk-UA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/uk-UA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/ur-PK/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/ur-PK/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/uz-Latn-UZ/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/uz-Latn-UZ/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/vi-VN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/vi-VN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/zh-CN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/zh-CN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/zh-TW/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/zh-TW/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/Microsoft.UI.Xaml.Controls.dll", "runtimes-framework/win-x64/native/Microsoft.UI.Xaml.Controls.pri", "runtimes-framework/win-x64/native/Microsoft.UI.Xaml.Internal.dll", "runtimes-framework/win-x64/native/Microsoft.UI.Xaml.Phone.dll", "runtimes-framework/win-x64/native/Microsoft.UI.Xaml/Assets/NoiseAsset_256x256_PNG.png", "runtimes-framework/win-x64/native/Microsoft.UI.Xaml/Assets/map.html", "runtimes-framework/win-x64/native/Microsoft.ui.xaml.dll", "runtimes-framework/win-x64/native/Microsoft.ui.xaml.resources.19h1.dll", "runtimes-framework/win-x64/native/Microsoft.ui.xaml.resources.common.dll", "runtimes-framework/win-x64/native/WinUIEdit.dll", "runtimes-framework/win-x64/native/af-ZA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/af-ZA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/am-ET/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/am-ET/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/ar-SA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/ar-SA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/as-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/as-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/az-Latn-AZ/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/az-Latn-AZ/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/bg-BG/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/bg-BG/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/bn-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/bn-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/bs-Latn-BA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/bs-Latn-BA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/ca-ES/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/ca-ES/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/ca-Es-VALENCIA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/ca-Es-VALENCIA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/cs-CZ/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/cs-CZ/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/cy-GB/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/cy-GB/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/da-DK/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/da-DK/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/de-DE/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/de-DE/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/el-GR/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/el-GR/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/en-GB/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/en-GB/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/en-us/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/en-us/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/es-ES/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/es-ES/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/es-MX/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/es-MX/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/et-EE/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/et-EE/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/eu-ES/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/eu-ES/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/fa-IR/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/fa-IR/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/fi-FI/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/fi-FI/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/fil-PH/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/fil-PH/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/fr-CA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/fr-CA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/fr-FR/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/fr-FR/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/ga-IE/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/ga-IE/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/gd-gb/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/gd-gb/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/gl-ES/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/gl-ES/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/gu-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/gu-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/he-IL/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/he-IL/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/hi-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/hi-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/hr-HR/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/hr-HR/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/hu-HU/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/hu-HU/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/hy-AM/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/hy-AM/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/id-ID/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/id-ID/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/is-IS/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/is-IS/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/it-IT/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/it-IT/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/ja-JP/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/ja-JP/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/ka-GE/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/ka-GE/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/kk-KZ/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/kk-KZ/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/km-KH/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/km-KH/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/kn-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/kn-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/ko-KR/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/ko-KR/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/kok-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/kok-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/lb-LU/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/lb-LU/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/lo-LA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/lo-LA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/lt-LT/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/lt-LT/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/lv-LV/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/lv-LV/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/mi-NZ/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/mi-NZ/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/mk-MK/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/mk-MK/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/ml-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/ml-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/mr-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/mr-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/ms-MY/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/ms-MY/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/mt-MT/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/mt-MT/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/nb-NO/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/nb-NO/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/ne-NP/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/ne-NP/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/nl-NL/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/nl-NL/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/nn-NO/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/nn-NO/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/or-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/or-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/pa-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/pa-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/pl-PL/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/pl-PL/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/pt-BR/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/pt-BR/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/pt-PT/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/pt-PT/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/quz-PE/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/quz-PE/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/ro-RO/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/ro-RO/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/ru-RU/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/ru-RU/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/sk-SK/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/sk-SK/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/sl-SI/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/sl-SI/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/sq-AL/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/sq-AL/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/sr-Cyrl-BA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/sr-Cyrl-BA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/sr-Cyrl-RS/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/sr-Cyrl-RS/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/sr-Latn-RS/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/sr-Latn-RS/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/sv-SE/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/sv-SE/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/ta-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/ta-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/te-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/te-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/th-TH/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/th-TH/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/tr-TR/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/tr-TR/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/tt-RU/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/tt-RU/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/ug-CN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/ug-CN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/uk-UA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/uk-UA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/ur-PK/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/ur-PK/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/uz-Latn-UZ/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/uz-Latn-UZ/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/vi-VN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/vi-VN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/zh-CN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/zh-CN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/zh-TW/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/zh-TW/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/Microsoft.UI.Xaml.Controls.dll", "runtimes-framework/win-x86/native/Microsoft.UI.Xaml.Controls.pri", "runtimes-framework/win-x86/native/Microsoft.UI.Xaml.Internal.dll", "runtimes-framework/win-x86/native/Microsoft.UI.Xaml.Phone.dll", "runtimes-framework/win-x86/native/Microsoft.UI.Xaml/Assets/NoiseAsset_256x256_PNG.png", "runtimes-framework/win-x86/native/Microsoft.UI.Xaml/Assets/map.html", "runtimes-framework/win-x86/native/Microsoft.ui.xaml.dll", "runtimes-framework/win-x86/native/Microsoft.ui.xaml.resources.19h1.dll", "runtimes-framework/win-x86/native/Microsoft.ui.xaml.resources.common.dll", "runtimes-framework/win-x86/native/WinUIEdit.dll", "runtimes-framework/win-x86/native/af-ZA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/af-ZA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/am-ET/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/am-ET/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/ar-SA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/ar-SA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/as-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/as-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/az-Latn-AZ/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/az-Latn-AZ/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/bg-BG/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/bg-BG/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/bn-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/bn-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/bs-Latn-BA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/bs-Latn-BA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/ca-ES/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/ca-ES/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/ca-Es-VALENCIA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/ca-Es-VALENCIA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/cs-CZ/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/cs-CZ/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/cy-GB/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/cy-GB/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/da-DK/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/da-DK/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/de-DE/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/de-DE/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/el-GR/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/el-GR/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/en-GB/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/en-GB/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/en-us/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/en-us/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/es-ES/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/es-ES/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/es-MX/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/es-MX/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/et-EE/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/et-EE/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/eu-ES/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/eu-ES/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/fa-IR/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/fa-IR/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/fi-FI/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/fi-FI/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/fil-PH/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/fil-PH/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/fr-CA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/fr-CA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/fr-FR/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/fr-FR/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/ga-IE/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/ga-IE/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/gd-gb/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/gd-gb/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/gl-ES/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/gl-ES/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/gu-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/gu-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/he-IL/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/he-IL/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/hi-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/hi-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/hr-HR/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/hr-HR/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/hu-HU/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/hu-HU/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/hy-AM/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/hy-AM/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/id-ID/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/id-ID/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/is-IS/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/is-IS/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/it-IT/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/it-IT/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/ja-JP/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/ja-JP/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/ka-GE/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/ka-GE/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/kk-KZ/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/kk-KZ/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/km-KH/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/km-KH/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/kn-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/kn-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/ko-KR/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/ko-KR/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/kok-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/kok-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/lb-LU/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/lb-LU/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/lo-LA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/lo-LA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/lt-LT/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/lt-LT/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/lv-LV/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/lv-LV/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/mi-NZ/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/mi-NZ/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/mk-MK/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/mk-MK/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/ml-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/ml-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/mr-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/mr-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/ms-MY/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/ms-MY/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/mt-MT/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/mt-MT/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/nb-NO/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/nb-NO/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/ne-NP/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/ne-NP/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/nl-NL/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/nl-NL/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/nn-NO/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/nn-NO/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/or-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/or-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/pa-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/pa-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/pl-PL/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/pl-PL/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/pt-BR/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/pt-BR/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/pt-PT/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/pt-PT/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/quz-PE/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/quz-PE/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/ro-RO/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/ro-RO/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/ru-RU/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/ru-RU/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/sk-SK/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/sk-SK/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/sl-SI/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/sl-SI/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/sq-AL/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/sq-AL/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/sr-Cyrl-BA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/sr-Cyrl-BA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/sr-Cyrl-RS/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/sr-Cyrl-RS/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/sr-Latn-RS/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/sr-Latn-RS/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/sv-SE/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/sv-SE/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/ta-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/ta-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/te-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/te-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/th-TH/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/th-TH/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/tr-TR/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/tr-TR/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/tt-RU/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/tt-RU/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/ug-CN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/ug-CN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/uk-UA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/uk-UA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/ur-PK/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/ur-PK/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/uz-Latn-UZ/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/uz-Latn-UZ/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/vi-VN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/vi-VN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/zh-CN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/zh-CN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/zh-TW/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/zh-TW/Microsoft.ui.xaml.dll.mui", "tools/NOTICE.txt", "tools/arm64/GenXbf.dll", "tools/arm64ec/GenXbf.dll", "tools/net472/Microsoft.Bcl.AsyncInterfaces.dll", "tools/net472/Microsoft.Build.Framework.dll", "tools/net472/Microsoft.Build.Utilities.Core.dll", "tools/net472/Microsoft.Build.dll", "tools/net472/Microsoft.UI.Xaml.Markup.Compiler.IO.dll", "tools/net472/Microsoft.UI.Xaml.Markup.Compiler.MSBuildInterop.dll", "tools/net472/Microsoft.UI.Xaml.Markup.Compiler.dll", "tools/net472/System.Buffers.dll", "tools/net472/System.Collections.Immutable.dll", "tools/net472/System.Memory.dll", "tools/net472/System.Numerics.Vectors.dll", "tools/net472/System.Reflection.Metadata.dll", "tools/net472/System.Runtime.CompilerServices.Unsafe.dll", "tools/net472/System.Text.Encodings.Web.dll", "tools/net472/System.Text.Json.dll", "tools/net472/System.Threading.Tasks.Dataflow.dll", "tools/net472/System.Threading.Tasks.Extensions.dll", "tools/net472/XamlCompiler.exe", "tools/net472/XamlCompiler.exe.config", "tools/net6.0/Microsoft.Bcl.AsyncInterfaces.dll", "tools/net6.0/Microsoft.UI.Xaml.Markup.Compiler.IO.dll", "tools/net6.0/Microsoft.UI.Xaml.Markup.Compiler.MSBuildInterop.dll", "tools/net6.0/Microsoft.UI.Xaml.Markup.Compiler.dll", "tools/net6.0/System.Runtime.CompilerServices.Unsafe.dll", "tools/net6.0/System.Text.Encodings.Web.dll", "tools/net6.0/System.Text.Json.dll", "tools/x64/GenXbf.dll", "tools/x86/GenXbf.dll"]}}, "projectFileDependencyGroups": {"net8.0-windows10.0.19041": ["Microsoft.Windows.SDK.BuildTools >= 10.0.26100.4948", "Microsoft.WindowsAppSDK >= 1.8.250907003"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Software3\\PrestoIves\\PrestoIves\\PrestoIves\\PrestoIves.csproj", "projectName": "PrestoIves", "projectPath": "D:\\Software3\\PrestoIves\\PrestoIves\\PrestoIves\\PrestoIves.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Software3\\PrestoIves\\PrestoIves\\PrestoIves\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows10.0.19041.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows10.0.19041": {"targetAlias": "net8.0-windows10.0.19041.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows10.0.19041": {"targetAlias": "net8.0-windows10.0.19041.0", "dependencies": {"Microsoft.Windows.SDK.BuildTools": {"target": "Package", "version": "[10.0.26100.4948, )"}, "Microsoft.WindowsAppSDK": {"target": "Package", "version": "[1.8.250907003, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Runtime.win-arm64", "version": "[8.0.19, 8.0.19]"}, {"name": "Microsoft.AspNetCore.App.Runtime.win-x64", "version": "[8.0.19, 8.0.19]"}, {"name": "Microsoft.AspNetCore.App.Runtime.win-x86", "version": "[8.0.19, 8.0.19]"}, {"name": "Microsoft.NETCore.App.Runtime.win-arm64", "version": "[8.0.19, 8.0.19]"}, {"name": "Microsoft.NETCore.App.Runtime.win-x64", "version": "[8.0.19, 8.0.19]"}, {"name": "Microsoft.NETCore.App.Runtime.win-x86", "version": "[8.0.19, 8.0.19]"}, {"name": "Microsoft.Windows.SDK.NET.Ref", "version": "[10.0.19041.57, 10.0.19041.57]"}, {"name": "Microsoft.WindowsDesktop.App.Runtime.win-arm64", "version": "[8.0.19, 8.0.19]"}, {"name": "Microsoft.WindowsDesktop.App.Runtime.win-x64", "version": "[8.0.19, 8.0.19]"}, {"name": "Microsoft.WindowsDesktop.App.Runtime.win-x86", "version": "[8.0.19, 8.0.19]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.Windows.SDK.NET.Ref.Windows": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"win-arm64": {"#import": []}, "win-x64": {"#import": []}, "win-x86": {"#import": []}}}}