﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ApplicationDefinition Include="App.xaml" />
  </ItemGroup>
  <ItemGroup>
    <Page Include="MainWindow.xaml" />
    <Page Include="MainPage.xaml" />
    <Page Include="SettingsPage.xaml" />
    <Page Include="Scenario1_ToastWithAvatar.xaml" />
    <Page Include="Scenario2_ToastWithTextBox.xaml" />
    <Page Include="Styles.xaml" />
  </ItemGroup>
  <ItemGroup>
    <Midl Include="Project.idl" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="pch.cpp" />
    <ClCompile Include="$(GeneratedFilesDir)module.g.cpp" />
    <ClCompile Include="SampleConfiguration.cpp" />
    <ClCompile Include="NotifyUser.cpp" />
    <ClCompile Include="Notifications\NotificationManager.cpp">
      <Filter>Notifications</Filter>
    </ClCompile>
    <ClCompile Include="Notifications\ToastWithAvatar.cpp">
      <Filter>Notifications</Filter>
    </ClCompile>
    <ClCompile Include="Notifications\ToastWithTextBox.cpp">
      <Filter>Notifications</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="pch.h" />
    <ClInclude Include="SampleConfiguration.h" />
    <ClInclude Include="NotifyUser.h" />
    <ClInclude Include="Notifications\Common.h">
      <Filter>Notifications</Filter>
    </ClInclude>
    <ClInclude Include="Notifications\NotificationManager.h">
      <Filter>Notifications</Filter>
    </ClInclude>
    <ClInclude Include="Notifications\ToastWithAvatar.h">
      <Filter>Notifications</Filter>
    </ClInclude>
    <ClInclude Include="Notifications\ToastWithTextBox.h">
      <Filter>Notifications</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <Image Include="Assets\Wide310x150Logo.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\logo.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\Square150x150Logo.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\Square44x44Logo.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\SplashScreen.png">
      <Filter>Assets</Filter>
    </Image>
    <Image Include="Assets\windows-sdk.ico">
      <Filter>Assets</Filter>
    </Image>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Assets">
      <UniqueIdentifier>{$guid1}</UniqueIdentifier>
    </Filter>
    <Filter Include="Notifications">
      <UniqueIdentifier>{61c8c50e-14b1-4504-b7ad-935b944692e9}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <Manifest Include="app.manifest" />
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config" />
  </ItemGroup>
</Project>