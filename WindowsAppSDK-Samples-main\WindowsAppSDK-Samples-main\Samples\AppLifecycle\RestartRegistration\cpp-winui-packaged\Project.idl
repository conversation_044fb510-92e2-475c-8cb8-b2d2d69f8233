﻿// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.

namespace cpp_winui_packaged
{
    /* The following code is scenario/feature-specific IDL.
    Samples authors should modify these runtime classes as appropriate. */

    [default_interface]
    runtimeclass S1_UpdateReboot : Microsoft.UI.Xaml.Controls.Page
    {
        S1_UpdateReboot();        
    }

    [default_interface]
    runtimeclass S2_CrashRecovery : Microsoft.UI.Xaml.Controls.Page
    {
        S2_CrashRecovery();
    }

    [default_interface]
    runtimeclass S3_RebootTypeDetection : Microsoft.UI.Xaml.Controls.Page
    {
        S3_RebootTypeDetection();
    }

     /* The following code is template-specific IDL.
     These runtime classes are the same across all C++/WinRT WinUI samples. */

    runtimeclass MainPage : Microsoft.UI.Xaml.Controls.Page
    {
        MainPage();
        static MainPage Current();
        void NotifyUser(String strMessage, Microsoft.UI.Xaml.Controls.InfoBarSeverity severity);
    }

    // To use Scenario in a winrt::Windows::Foundation::Collections::IVector<Scenario>, <PERSON>ena<PERSON> should be a WinRT type
    struct Scenario
    {
        String Title;
        String ClassName;
    };

    [default_interface]
    runtimeclass MainWindow : Microsoft.UI.Xaml.Window
    {
        MainWindow();
    }

    [default_interface]
    runtimeclass SettingsPage : Microsoft.UI.Xaml.Controls.Page
    {
        SettingsPage();
    }
}
