﻿<Window
    x:Class="cs_winui_packaged.MainWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:cs_winui_packaged"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d">

    <StackPanel Orientation="Vertical" HorizontalAlignment="Center" VerticalAlignment="Center">
        <StackPanel Orientation="Vertical" HorizontalAlignment="Center" VerticalAlignment="Center">
            <TextBox x:Name="argumentsInput"
                     Header="Enter restart arguments:" PlaceholderText=""
                     Width="300"/>

            <Button x:Name="myButton" Click="myButton_Click" HorizontalAlignment="Center">Restart</Button>
        </StackPanel>

        <StackPanel Orientation="Vertical" HorizontalAlignment="Center" VerticalAlignment="Center" Padding="10,10,10,10">
            <TextBlock x:Name="statusText" FontWeight="Bold">Environment.GetCommandLineArgs():</TextBlock>
            <TextBlock x:Name="commandLineArgsText">No Arguments</TextBlock>
        </StackPanel>

        <StackPanel Orientation="Vertical" HorizontalAlignment="Center" VerticalAlignment="Center" Padding="10,10,10,10">
            <TextBlock x:Name="statusText2" FontWeight="Bold">AppInstance.GetCurrent().GetActivatedEventArgs():</TextBlock>
            <TextBlock x:Name="ActivatedEventArgsText">No Arguments</TextBlock>
        </StackPanel>


    </StackPanel>
</Window>
