﻿<Application
    x:Class="DynamicRefreshRateTool.App"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:DynamicRefreshRateTool">
    <Application.Resources>
        <ResourceDictionary>

            <ResourceDictionary.MergedDictionaries>
                <XamlControlsResources xmlns="using:Microsoft.UI.Xaml.Controls" />
            </ResourceDictionary.MergedDictionaries>

            <ResourceDictionary.ThemeDictionaries>
                <ResourceDictionary x:Key="Light">
                    <SolidColorBrush x:Key="BackgroundBrush" Color="#F0F0F0"/>
                    <SolidColorBrush x:Key="BackgroundBrush2" Color="#F6F6F6"/>
                    <SolidColorBrush x:Key="ForegroundBrush" Color="#FDFDFD"/>
                    <SolidColorBrush x:Key="BorderBrush" Color="#E5E5E5"/>
                    <SolidColorBrush x:Key="ChartMain" Color="{ThemeResource SystemAccentColor}"/>
                    <SolidColorBrush x:Key="ChartDash" Color="{ThemeResource SystemAccentColorLight1}"/>
                    <SolidColorBrush x:Key="ChartFill" Color="{ThemeResource SystemAccentColorLight3}"/>
                    <StaticResource x:Key="ControlExampleDisplayBrush" ResourceKey="SolidBackgroundFillColorBaseBrush" />

                    <SolidColorBrush x:Key="WindowCaptionBackground">#20A0A0A0</SolidColorBrush>
                    <SolidColorBrush x:Key="WindowCaptionBackgroundDisabled">#20A0A0A0</SolidColorBrush>
                </ResourceDictionary>

                <ResourceDictionary x:Key="Dark">
                    <SolidColorBrush x:Key="BackgroundBrush" Color="#202020"/>
                    <SolidColorBrush x:Key="BackgroundBrush2" Color="#262626"/>
                    <SolidColorBrush x:Key="ForegroundBrush" Color="#2B2B2B"/>
                    <SolidColorBrush x:Key="BorderBrush" Color="#1A1A1A"/>
                    <SolidColorBrush x:Key="ChartMain" Color="{ThemeResource SystemAccentColor}"/>
                    <SolidColorBrush x:Key="ChartDash" Color="{ThemeResource SystemAccentColorDark1}"/>
                    <SolidColorBrush x:Key="ChartFill" Color="{ThemeResource SystemAccentColorDark3}"/>
                    <StaticResource x:Key="ControlExampleDisplayBrush" ResourceKey="SolidBackgroundFillColorBaseBrush" />
                </ResourceDictionary>
            </ResourceDictionary.ThemeDictionaries>
        </ResourceDictionary>

    </Application.Resources>
</Application>
