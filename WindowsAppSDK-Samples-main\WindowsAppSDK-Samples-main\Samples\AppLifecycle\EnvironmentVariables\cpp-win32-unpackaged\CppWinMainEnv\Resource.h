// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.

#define IDS_APP_TITLE          103
#define IDR_MAINFRAME          128
#define IDI_MAINICON           107
#define IDI_SMALL              108
#define IDC_CLASSNAME          109
#define IDM_GETVARS            110
#define IDM_ADDVARS            111
#define IDM_REMOVEVARS         112
#define IDM_ADDTOPATH          113
#define IDM_REMOVEFROMPATH     114
#define IDM_ADDTOPATHEXT       115
#define IDM_REMOVEFROMPATHEXT  116
#define IDC_LISTBOX            120
#define IDC_MYICON             2
#ifndef IDC_STATIC
#define IDC_STATIC             -1
#endif
// Next default values for new objects
//
#ifdef APSTUDIO_INVOKED
#ifndef APSTUDIO_READONLY_SYMBOLS

#define _APS_NO_MFC                 130
#define _APS_NEXT_RESOURCE_VALUE    129
#define _APS_NEXT_COMMAND_VALUE     32771
#define _APS_NEXT_CONTROL_VALUE     1000
#define _APS_NEXT_SYMED_VALUE       121
#endif
#endif
