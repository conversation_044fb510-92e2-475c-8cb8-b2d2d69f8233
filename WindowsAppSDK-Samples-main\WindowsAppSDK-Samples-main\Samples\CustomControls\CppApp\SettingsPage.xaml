﻿<!-- Copyright (c) Microsoft Corporation.
     Licensed under the MIT License. -->
<Page
    x:Class="CppApp.SettingsPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:CppApp"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d">

    <Grid RowDefinitions="*,Auto">
        <ScrollViewer Background="{ThemeResource ApplicationPageBackgroundThemeBrush}">
            <StackPanel Spacing="10" Margin="10,10,10,10" >
                <TextBlock Text="Theme Mode" Style="{StaticResource SampleHeaderTextStyle}"/>
                <StackPanel x:Name="themePanel">
                    <RadioButton Checked="OnThemeRadioButtonChecked" Content="Light">
                        <RadioButton.Tag>
                            <ElementTheme>Light</ElementTheme>
                        </RadioButton.Tag>
                    </RadioButton>
                    <RadioButton Checked="OnThemeRadioButtonChecked" Content="Dark">
                        <RadioButton.Tag>
                            <ElementTheme>Dark</ElementTheme>
                        </RadioButton.Tag>
                    </RadioButton>
                    <RadioButton Checked="OnThemeRadioButtonChecked" Content="System Default">
                        <RadioButton.Tag>
                            <ElementTheme>Default</ElementTheme>
                        </RadioButton.Tag>
                    </RadioButton>
                </StackPanel>
            </StackPanel>
        </ScrollViewer>

        <StackPanel Spacing="10" Margin="10,10,10,10" Orientation="Vertical" VerticalAlignment="Bottom" Grid.Row="0">
            <TextBlock Text="&#xA9; Microsoft Corporation. All rights reserved." Style="{StaticResource CopyrightTextStyle}" TextWrapping="Wrap"/>
        </StackPanel>
    </Grid>
</Page>
